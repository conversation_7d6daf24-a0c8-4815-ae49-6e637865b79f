self.__BUILD_MANIFEST = (function(a){return {__rewrites:{afterFiles:[{has:a,source:"\u002F:nextInternalLocale(en)\u002Fsitemap.xml",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fsitemap.xml"},{has:a,source:"\u002F:nextInternalLocale(en)\u002Frobots.txt",destination:"\u002F:nextInternalLocale\u002Fapi\u002Frobots.txt"},{has:a,source:"\u002F:nextInternalLocale(en)\u002Fcommunities",destination:"\u002F:nextInternalLocale\u002Fcommunities"},{has:a,source:"\u002F:nextInternalLocale(en)\u002Fcampaigns",destination:"\u002F:nextInternalLocale\u002Fcampaigns"},{has:a,source:"\u002F:nextInternalLocale(en)\u002Fleaderboard",destination:"\u002F:nextInternalLocale\u002Fleaderboard"},{has:a,source:"\u002F:nextInternalLocale(en)",destination:"\u002F:nextInternalLocale\u002Fexplore"}],beforeFiles:[],fallback:[]},"/_error":["static\u002Fchunks\u002Fpages\u002F_error.js"],"/[project]":["static\u002Fchunks\u002Fpages\u002F[project].js"],"/[project]/[event]":["static\u002Fchunks\u002Fpages\u002F[project]\u002F[event].js"],sortedPages:["\u002F_app","\u002F_error","\u002F[project]","\u002F[project]\u002F[event]"]}}(void 0));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()