"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ middlewareWrapperTemplate),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   restrictedPage: () => (/* binding */ restrictedPage)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @sentry/nextjs */ \"(middleware)/./node_modules/@sentry/nextjs/build/esm/edge/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n\nconst restrictedPage = \"/restricted-region\";\nasync function middleware$1(req) {\n    // Inorder to enable Geo Blocking restrictions, uncomment the following code\n    // const imagePattern = /\\.(jpg|jpeg|png|gif|webp|svg|ico)$/i; // Add any other image extensions as needed\n    // const videoPattern = /\\.(mp4|mov|avi|mkv|wmv)$/i; // Add any other video extensions as needed\n    // const blacklistedRegions = ['US', 'KP', 'CU', 'IR', 'SY'];\n    // if (\n    //   imagePattern.test(req.nextUrl.pathname) ||\n    //   videoPattern.test(req.nextUrl.pathname) ||\n    //   req.nextUrl.pathname === restrictedPage\n    // ) {\n    //   return NextResponse.next();\n    // }\n    // const response = await fetch(`${server}/auth/client-details`, {\n    //   method: 'GET',\n    //   headers: {\n    //     'Content-Type': 'application/json',\n    //     'x-airlyft-ip':\n    //       req.headers.get('x-forwarded-for') ||\n    //       req.headers.get('x-real-ip') ||\n    //       '',\n    //   },\n    // });\n    // const responseJson = await response.json();\n    // if (blacklistedRegions.includes(responseJson?.countryCode)) {\n    //   const url = new URL(restrictedPage, req.url);\n    //   url.searchParams.set('country', responseJson?.countryCode);\n    //   return NextResponse.redirect(url);\n    // }\n    if (req.nextUrl.pathname.endsWith(\"/default-quests\")) {\n        const projectPath = req.nextUrl.pathname.replace(\"/default-quests\", \"\");\n        const url = new URL(projectPath, req.url);\n        url.search = req.nextUrl.search;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        \"/((?!api|_next/static|_next/image|favicon.ico|404).*)\"\n    ]\n};\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    config: config,\n    default: middleware$1,\n    restrictedPage: restrictedPage\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles middleware files.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userApiModule = serverComponentModule ;\n\n// Default to undefined. It's possible for Next.js users to not define any exports/handlers in an API route. If that is\n// the case Next.js wil crash during runtime but the Sentry SDK should definitely not crash so we need tohandle it.\nlet userProvidedNamedHandler = undefined;\nlet userProvidedDefaultHandler = undefined;\n\nif ('middleware' in userApiModule && typeof userApiModule.middleware === 'function') {\n  // Handle when user defines via named ESM export: `export { middleware };`\n  userProvidedNamedHandler = userApiModule.middleware;\n} else if ('default' in userApiModule && typeof userApiModule.default === 'function') {\n  // Handle when user defines via ESM export: `export default myFunction;`\n  userProvidedDefaultHandler = userApiModule.default;\n} else if (typeof userApiModule === 'function') {\n  // Handle when user defines via CJS export: \"module.exports = myFunction;\"\n  userProvidedDefaultHandler = userApiModule;\n}\n\nconst middleware = userProvidedNamedHandler\n  ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__.wrapMiddlewareWithSentry(userProvidedNamedHandler)\n  : undefined;\nconst middlewareWrapperTemplate = userProvidedDefaultHandler ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_1__.wrapMiddlewareWithSentry(userProvidedDefaultHandler) : undefined;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});