"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Luckydraw_LuckydrawSlotBody_tsx";
exports.ids = ["components_Tasks_Luckydraw_LuckydrawSlotBody_tsx"];
exports.modules = {

/***/ "./components/SlotMachine.tsx":
/*!************************************!*\
  !*** ./components/SlotMachine.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SlotMachine = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ rewards, onSpinStart, onSpinEnd, resultIndex, slotIcons = [], initialPosition = null }, ref)=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const styleEl = document.createElement(\"style\");\n        const animations = [\n            `@keyframes spin1 {\r\n        0% { transform: translateY(0); }\r\n        25% { transform: translateY(-100%); }\r\n        50% { transform: translateY(-200%); }\r\n        75% { transform: translateY(-300%); }\r\n        100% { transform: translateY(0); }\r\n      }\r\n      .animate-spin-1 {\r\n        animation: spin1 0.5s ease-in-out 3;\r\n      }`,\n            `@keyframes spin2 {\r\n        0% { transform: translateY(0); }\r\n        25% { transform: translateY(-100%); }\r\n        50% { transform: translateY(-200%); }\r\n        75% { transform: translateY(-300%); }\r\n        100% { transform: translateY(0); }\r\n      }\r\n      .animate-spin-2 {\r\n        animation: spin2 0.5s ease-in-out 4;\r\n      }`,\n            `@keyframes spin3 {\r\n        0% { transform: translateY(0); }\r\n        25% { transform: translateY(-100%); }\r\n        50% { transform: translateY(-200%); }\r\n        75% { transform: translateY(-300%); }\r\n        100% { transform: translateY(0); }\r\n      }\r\n      .animate-spin-3 {\r\n        animation: spin3 0.5s ease-in-out 5;\r\n      }`\n        ];\n        styleEl.textContent = animations.join(\"\\n\");\n        document.head.appendChild(styleEl);\n        return ()=>{\n            document.head.removeChild(styleEl);\n        };\n    }, []);\n    const getInitialReels = ()=>{\n        if (initialPosition === null || !slotIcons || slotIcons.length === 0) {\n            return Array(3).fill(slotIcons && slotIcons.length > 0 ? slotIcons[0] : \"\");\n        }\n        if (initialPosition === 0) {\n            const sameSymbol = slotIcons[0] || \"\";\n            return [\n                sameSymbol,\n                sameSymbol,\n                sameSymbol\n            ];\n        } else if (initialPosition === 1) {\n            const firstSymbol = slotIcons[0] || \"\";\n            const secondSymbol = slotIcons.length > 1 ? slotIcons[1] : firstSymbol;\n            return [\n                firstSymbol,\n                firstSymbol,\n                secondSymbol\n            ];\n        } else if (initialPosition === 2) {\n            const firstSymbol = slotIcons[0] || \"\";\n            const secondSymbol = slotIcons.length > 1 ? slotIcons[1] : \"\";\n            const thirdSymbol = slotIcons.length > 2 ? slotIcons[2] : \"\";\n            return [\n                firstSymbol,\n                secondSymbol,\n                thirdSymbol\n            ];\n        } else {\n            return Array(3).fill(slotIcons[0] || \"\");\n        }\n    };\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        reels: getInitialReels(),\n        spinning: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialPosition !== null && !state.spinning) {\n            setState((prev)=>({\n                    ...prev,\n                    reels: getInitialReels()\n                }));\n        }\n    }, [\n        initialPosition,\n        slotIcons\n    ]);\n    const getRandomSymbol = ()=>{\n        if (slotIcons && slotIcons.length > 0) {\n            const randomIndex = Math.floor(Math.random() * slotIcons.length);\n            return slotIcons[randomIndex];\n        }\n        return \"\";\n    };\n    const spin = ()=>{\n        if (state.spinning) return;\n        onSpinStart();\n        const spinningSymbols = state.reels.map(()=>getRandomSymbol());\n        setState({\n            reels: spinningSymbols,\n            spinning: true\n        });\n        let newReels;\n        if (resultIndex === 0) {\n            const sameSymbol = slotIcons && slotIcons.length > 0 ? slotIcons[0] : \"\";\n            newReels = [\n                sameSymbol,\n                sameSymbol,\n                sameSymbol\n            ];\n        } else if (resultIndex === 1) {\n            const firstSymbol = slotIcons && slotIcons.length > 0 ? slotIcons[0] : \"\";\n            const secondSymbol = slotIcons && slotIcons.length > 1 ? slotIcons[1] : firstSymbol;\n            newReels = [\n                firstSymbol,\n                firstSymbol,\n                secondSymbol\n            ];\n        } else if (resultIndex === 2) {\n            const firstSymbol = slotIcons && slotIcons.length > 0 ? slotIcons[0] : \"\";\n            const secondSymbol = slotIcons && slotIcons.length > 1 ? slotIcons[1] : \"\";\n            const thirdSymbol = slotIcons && slotIcons.length > 2 ? slotIcons[2] : \"\";\n            newReels = [\n                firstSymbol,\n                secondSymbol,\n                thirdSymbol\n            ];\n        } else {\n            newReels = [\n                getRandomSymbol(),\n                getRandomSymbol(),\n                getRandomSymbol()\n            ];\n        }\n        setTimeout(()=>{\n            setState((prev)=>({\n                    ...prev,\n                    reels: [\n                        newReels[0],\n                        prev.reels[1],\n                        prev.reels[2]\n                    ]\n                }));\n        }, 1500);\n        setTimeout(()=>{\n            setState((prev)=>({\n                    ...prev,\n                    reels: [\n                        newReels[0],\n                        newReels[1],\n                        prev.reels[2]\n                    ]\n                }));\n        }, 2000);\n        setTimeout(()=>{\n            setState({\n                reels: newReels,\n                spinning: false\n            });\n            onSpinEnd();\n        }, 2500);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            spin\n        }));\n    const renderSymbol = (symbol)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: symbol,\n            alt: \"Slot Symbol\",\n            className: \"w-12 h-12 object-contain\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n            lineNumber: 193,\n            columnNumber: 9\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center p-8 rounded-xl shadow-lg max-w-md mx-auto border-4 border-yellow-500\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-4\",\n                children: state.reels.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-16 h-16 border border-white rounded-lg shadow overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `absolute inset-0 flex items-center justify-center text-4xl\r\n                  ${state.spinning ? `animate-spin-${index + 1}` : \"\"}`,\n                            children: renderSymbol(symbol)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 17\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 15\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n                lineNumber: 204,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n            lineNumber: 203,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SlotMachine.tsx\",\n        lineNumber: 202,\n        columnNumber: 7\n    }, undefined);\n});\nSlotMachine.displayName = \"SlotMachine\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SlotMachine);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Nsb3RNYWNoaW5lLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDNkU7QUFzQjdFLE1BQU1JLDRCQUFjSixpREFBVUEsQ0FDNUIsQ0FDRSxFQUNFSyxPQUFPLEVBQ1BDLFdBQVcsRUFDWEMsU0FBUyxFQUNUQyxXQUFXLEVBQ1hDLFlBQVksRUFBRSxFQUNkQyxrQkFBa0IsSUFBSSxFQUN2QixFQUNEQztJQUVBVixnREFBU0EsQ0FBQztRQUNSLE1BQU1XLFVBQVVDLFNBQVNDLGFBQWEsQ0FBQztRQUN2QyxNQUFNQyxhQUFhO1lBQ2pCLENBQUM7Ozs7Ozs7OztPQVNGLENBQUM7WUFDQSxDQUFDOzs7Ozs7Ozs7T0FTRixDQUFDO1lBQ0EsQ0FBQzs7Ozs7Ozs7O09BU0YsQ0FBQztTQUNEO1FBRURILFFBQVFJLFdBQVcsR0FBR0QsV0FBV0UsSUFBSSxDQUFDO1FBQ3RDSixTQUFTSyxJQUFJLENBQUNDLFdBQVcsQ0FBQ1A7UUFFMUIsT0FBTztZQUNMQyxTQUFTSyxJQUFJLENBQUNFLFdBQVcsQ0FBQ1I7UUFDNUI7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNUyxrQkFBa0I7UUFDdEIsSUFBSVgsb0JBQW9CLFFBQVEsQ0FBQ0QsYUFBYUEsVUFBVWEsTUFBTSxLQUFLLEdBQUc7WUFDcEUsT0FBT0MsTUFBTSxHQUFHQyxJQUFJLENBQ2xCZixhQUFhQSxVQUFVYSxNQUFNLEdBQUcsSUFBSWIsU0FBUyxDQUFDLEVBQUUsR0FBRztRQUV2RDtRQUVBLElBQUlDLG9CQUFvQixHQUFHO1lBQ3pCLE1BQU1lLGFBQWFoQixTQUFTLENBQUMsRUFBRSxJQUFJO1lBQ25DLE9BQU87Z0JBQUNnQjtnQkFBWUE7Z0JBQVlBO2FBQVc7UUFDN0MsT0FBTyxJQUFJZixvQkFBb0IsR0FBRztZQUNoQyxNQUFNZ0IsY0FBY2pCLFNBQVMsQ0FBQyxFQUFFLElBQUk7WUFDcEMsTUFBTWtCLGVBQWVsQixVQUFVYSxNQUFNLEdBQUcsSUFBSWIsU0FBUyxDQUFDLEVBQUUsR0FBR2lCO1lBQzNELE9BQU87Z0JBQUNBO2dCQUFhQTtnQkFBYUM7YUFBYTtRQUNqRCxPQUFPLElBQUlqQixvQkFBb0IsR0FBRztZQUNoQyxNQUFNZ0IsY0FBY2pCLFNBQVMsQ0FBQyxFQUFFLElBQUk7WUFDcEMsTUFBTWtCLGVBQWVsQixVQUFVYSxNQUFNLEdBQUcsSUFBSWIsU0FBUyxDQUFDLEVBQUUsR0FBRztZQUMzRCxNQUFNbUIsY0FBY25CLFVBQVVhLE1BQU0sR0FBRyxJQUFJYixTQUFTLENBQUMsRUFBRSxHQUFHO1lBQzFELE9BQU87Z0JBQUNpQjtnQkFBYUM7Z0JBQWNDO2FBQVk7UUFDakQsT0FBTztZQUNMLE9BQU9MLE1BQU0sR0FBR0MsSUFBSSxDQUFDZixTQUFTLENBQUMsRUFBRSxJQUFJO1FBQ3ZDO0lBQ0Y7SUFFQSxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUczQiwrQ0FBUUEsQ0FBbUI7UUFDbkQ0QixPQUFPVjtRQUNQVyxVQUFVO0lBQ1o7SUFFQS9CLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVMsb0JBQW9CLFFBQVEsQ0FBQ21CLE1BQU1HLFFBQVEsRUFBRTtZQUMvQ0YsU0FBUyxDQUFDRyxPQUFVO29CQUNsQixHQUFHQSxJQUFJO29CQUNQRixPQUFPVjtnQkFDVDtRQUNGO0lBQ0YsR0FBRztRQUFDWDtRQUFpQkQ7S0FBVTtJQUUvQixNQUFNeUIsa0JBQWtCO1FBQ3RCLElBQUl6QixhQUFhQSxVQUFVYSxNQUFNLEdBQUcsR0FBRztZQUNyQyxNQUFNYSxjQUFjQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSzdCLFVBQVVhLE1BQU07WUFDL0QsT0FBT2IsU0FBUyxDQUFDMEIsWUFBWTtRQUMvQjtRQUNBLE9BQU87SUFDVDtJQUVBLE1BQU1JLE9BQU87UUFDWCxJQUFJVixNQUFNRyxRQUFRLEVBQUU7UUFFcEIxQjtRQUVBLE1BQU1rQyxrQkFBZ0NYLE1BQU1FLEtBQUssQ0FBQ1UsR0FBRyxDQUFDLElBQ3BEUDtRQUdGSixTQUFTO1lBQ1BDLE9BQU9TO1lBQ1BSLFVBQVU7UUFDWjtRQUVBLElBQUlVO1FBRUosSUFBSWxDLGdCQUFnQixHQUFHO1lBQ3JCLE1BQU1pQixhQUNKaEIsYUFBYUEsVUFBVWEsTUFBTSxHQUFHLElBQUliLFNBQVMsQ0FBQyxFQUFFLEdBQUc7WUFDckRpQyxXQUFXO2dCQUFDakI7Z0JBQVlBO2dCQUFZQTthQUFXO1FBQ2pELE9BQU8sSUFBSWpCLGdCQUFnQixHQUFHO1lBQzVCLE1BQU1rQixjQUNKakIsYUFBYUEsVUFBVWEsTUFBTSxHQUFHLElBQUliLFNBQVMsQ0FBQyxFQUFFLEdBQUc7WUFDckQsTUFBTWtCLGVBQ0psQixhQUFhQSxVQUFVYSxNQUFNLEdBQUcsSUFBSWIsU0FBUyxDQUFDLEVBQUUsR0FBR2lCO1lBQ3JEZ0IsV0FBVztnQkFBQ2hCO2dCQUFhQTtnQkFBYUM7YUFBYTtRQUNyRCxPQUFPLElBQUluQixnQkFBZ0IsR0FBRztZQUM1QixNQUFNa0IsY0FDSmpCLGFBQWFBLFVBQVVhLE1BQU0sR0FBRyxJQUFJYixTQUFTLENBQUMsRUFBRSxHQUFHO1lBQ3JELE1BQU1rQixlQUNKbEIsYUFBYUEsVUFBVWEsTUFBTSxHQUFHLElBQUliLFNBQVMsQ0FBQyxFQUFFLEdBQUc7WUFDckQsTUFBTW1CLGNBQ0puQixhQUFhQSxVQUFVYSxNQUFNLEdBQUcsSUFBSWIsU0FBUyxDQUFDLEVBQUUsR0FBRztZQUNyRGlDLFdBQVc7Z0JBQUNoQjtnQkFBYUM7Z0JBQWNDO2FBQVk7UUFDckQsT0FBTztZQUNMYyxXQUFXO2dCQUFDUjtnQkFBbUJBO2dCQUFtQkE7YUFBa0I7UUFDdEU7UUFFQVMsV0FBVztZQUNUYixTQUFTLENBQUNHLE9BQVU7b0JBQ2xCLEdBQUdBLElBQUk7b0JBQ1BGLE9BQU87d0JBQUNXLFFBQVEsQ0FBQyxFQUFFO3dCQUFFVCxLQUFLRixLQUFLLENBQUMsRUFBRTt3QkFBRUUsS0FBS0YsS0FBSyxDQUFDLEVBQUU7cUJBQUM7Z0JBQ3BEO1FBQ0YsR0FBRztRQUVIWSxXQUFXO1lBQ1RiLFNBQVMsQ0FBQ0csT0FBVTtvQkFDbEIsR0FBR0EsSUFBSTtvQkFDUEYsT0FBTzt3QkFBQ1csUUFBUSxDQUFDLEVBQUU7d0JBQUVBLFFBQVEsQ0FBQyxFQUFFO3dCQUFFVCxLQUFLRixLQUFLLENBQUMsRUFBRTtxQkFBQztnQkFDbEQ7UUFDRixHQUFHO1FBRUhZLFdBQVc7WUFDVGIsU0FBUztnQkFDUEMsT0FBT1c7Z0JBQ1BWLFVBQVU7WUFDWjtZQUVBekI7UUFDRixHQUFHO0lBQ0w7SUFFQUwsMERBQW1CQSxDQUFDUyxLQUFLLElBQU87WUFDOUI0QjtRQUNGO0lBRUEsTUFBTUssZUFBZSxDQUFDQztRQUNwQixxQkFDRSw4REFBQ0M7WUFDQ0MsS0FBS0Y7WUFDTEcsS0FBSTtZQUNKQyxXQUFVOzs7Ozs7SUFHaEI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTtrQkFDYiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ1pwQixNQUFNRSxLQUFLLENBQUNVLEdBQUcsQ0FBQyxDQUFDSSxRQUFRTSxzQkFDeEIsOERBQUNEO3dCQUVDRCxXQUFVO2tDQUVWLDRFQUFDQzs0QkFDQ0QsV0FBVyxDQUFDO2tCQUNaLEVBQUVwQixNQUFNRyxRQUFRLEdBQUcsQ0FBQyxhQUFhLEVBQUVtQixRQUFRLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQztzQ0FFcERQLGFBQWFDOzs7Ozs7dUJBUFhNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWVuQjtBQUdGL0MsWUFBWWdELFdBQVcsR0FBRztBQUUxQixpRUFBZWhELFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1Nsb3RNYWNoaW5lLnRzeD9mYTNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEx1Y2t5ZHJhd1Jld2FyZCB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuaW1wb3J0IHsgZm9yd2FyZFJlZiwgdXNlRWZmZWN0LCB1c2VJbXBlcmF0aXZlSGFuZGxlLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbnR5cGUgU2xvdFN5bWJvbCA9IHN0cmluZztcclxuXHJcbmludGVyZmFjZSBTbG90TWFjaGluZVByb3BzIHtcclxuICByZXdhcmRzOiBMdWNreWRyYXdSZXdhcmRbXTtcclxuICBvblNwaW5TdGFydDogKCkgPT4gdm9pZDtcclxuICBvblNwaW5FbmQ6ICgpID0+IHZvaWQ7XHJcbiAgcmVzdWx0SW5kZXg6IG51bWJlciB8IG51bGw7XHJcbiAgc2xvdEljb25zPzogc3RyaW5nW10gfCBudWxsO1xyXG4gIGluaXRpYWxQb3NpdGlvbj86IG51bWJlciB8IG51bGw7XHJcbn1cclxuXHJcbmludGVyZmFjZSBTbG90TWFjaGluZVN0YXRlIHtcclxuICByZWVsczogU2xvdFN5bWJvbFtdO1xyXG4gIHNwaW5uaW5nOiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFNsb3RNYWNoaW5lUmVmIHtcclxuICBzcGluOiAoKSA9PiB2b2lkO1xyXG59XHJcblxyXG5jb25zdCBTbG90TWFjaGluZSA9IGZvcndhcmRSZWY8U2xvdE1hY2hpbmVSZWYsIFNsb3RNYWNoaW5lUHJvcHM+KFxyXG4gIChcclxuICAgIHtcclxuICAgICAgcmV3YXJkcyxcclxuICAgICAgb25TcGluU3RhcnQsXHJcbiAgICAgIG9uU3BpbkVuZCxcclxuICAgICAgcmVzdWx0SW5kZXgsXHJcbiAgICAgIHNsb3RJY29ucyA9IFtdLFxyXG4gICAgICBpbml0aWFsUG9zaXRpb24gPSBudWxsLFxyXG4gICAgfSxcclxuICAgIHJlZixcclxuICApID0+IHtcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHN0eWxlRWwgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzdHlsZScpO1xyXG4gICAgICBjb25zdCBhbmltYXRpb25zID0gW1xyXG4gICAgICAgIGBAa2V5ZnJhbWVzIHNwaW4xIHtcclxuICAgICAgICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfVxyXG4gICAgICAgIDI1JSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTAwJSk7IH1cclxuICAgICAgICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTIwMCUpOyB9XHJcbiAgICAgICAgNzUlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zMDAlKTsgfVxyXG4gICAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7IH1cclxuICAgICAgfVxyXG4gICAgICAuYW5pbWF0ZS1zcGluLTEge1xyXG4gICAgICAgIGFuaW1hdGlvbjogc3BpbjEgMC41cyBlYXNlLWluLW91dCAzO1xyXG4gICAgICB9YCxcclxuICAgICAgICBgQGtleWZyYW1lcyBzcGluMiB7XHJcbiAgICAgICAgMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7IH1cclxuICAgICAgICAyNSUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwMCUpOyB9XHJcbiAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yMDAlKTsgfVxyXG4gICAgICAgIDc1JSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMzAwJSk7IH1cclxuICAgICAgICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9XHJcbiAgICAgIH1cclxuICAgICAgLmFuaW1hdGUtc3Bpbi0yIHtcclxuICAgICAgICBhbmltYXRpb246IHNwaW4yIDAuNXMgZWFzZS1pbi1vdXQgNDtcclxuICAgICAgfWAsXHJcbiAgICAgICAgYEBrZXlmcmFtZXMgc3BpbjMge1xyXG4gICAgICAgIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOyB9XHJcbiAgICAgICAgMjUlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xMDAlKTsgfVxyXG4gICAgICAgIDUwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjAwJSk7IH1cclxuICAgICAgICA3NSUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTMwMCUpOyB9XHJcbiAgICAgICAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfVxyXG4gICAgICB9XHJcbiAgICAgIC5hbmltYXRlLXNwaW4tMyB7XHJcbiAgICAgICAgYW5pbWF0aW9uOiBzcGluMyAwLjVzIGVhc2UtaW4tb3V0IDU7XHJcbiAgICAgIH1gLFxyXG4gICAgICBdO1xyXG5cclxuICAgICAgc3R5bGVFbC50ZXh0Q29udGVudCA9IGFuaW1hdGlvbnMuam9pbignXFxuJyk7XHJcbiAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc3R5bGVFbCk7XHJcblxyXG4gICAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAgIGRvY3VtZW50LmhlYWQucmVtb3ZlQ2hpbGQoc3R5bGVFbCk7XHJcbiAgICAgIH07XHJcbiAgICB9LCBbXSk7XHJcblxyXG4gICAgY29uc3QgZ2V0SW5pdGlhbFJlZWxzID0gKCk6IFNsb3RTeW1ib2xbXSA9PiB7XHJcbiAgICAgIGlmIChpbml0aWFsUG9zaXRpb24gPT09IG51bGwgfHwgIXNsb3RJY29ucyB8fCBzbG90SWNvbnMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgcmV0dXJuIEFycmF5KDMpLmZpbGwoXHJcbiAgICAgICAgICBzbG90SWNvbnMgJiYgc2xvdEljb25zLmxlbmd0aCA+IDAgPyBzbG90SWNvbnNbMF0gOiAnJyxcclxuICAgICAgICApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoaW5pdGlhbFBvc2l0aW9uID09PSAwKSB7XHJcbiAgICAgICAgY29uc3Qgc2FtZVN5bWJvbCA9IHNsb3RJY29uc1swXSB8fCAnJztcclxuICAgICAgICByZXR1cm4gW3NhbWVTeW1ib2wsIHNhbWVTeW1ib2wsIHNhbWVTeW1ib2xdO1xyXG4gICAgICB9IGVsc2UgaWYgKGluaXRpYWxQb3NpdGlvbiA9PT0gMSkge1xyXG4gICAgICAgIGNvbnN0IGZpcnN0U3ltYm9sID0gc2xvdEljb25zWzBdIHx8ICcnO1xyXG4gICAgICAgIGNvbnN0IHNlY29uZFN5bWJvbCA9IHNsb3RJY29ucy5sZW5ndGggPiAxID8gc2xvdEljb25zWzFdIDogZmlyc3RTeW1ib2w7XHJcbiAgICAgICAgcmV0dXJuIFtmaXJzdFN5bWJvbCwgZmlyc3RTeW1ib2wsIHNlY29uZFN5bWJvbF07XHJcbiAgICAgIH0gZWxzZSBpZiAoaW5pdGlhbFBvc2l0aW9uID09PSAyKSB7XHJcbiAgICAgICAgY29uc3QgZmlyc3RTeW1ib2wgPSBzbG90SWNvbnNbMF0gfHwgJyc7XHJcbiAgICAgICAgY29uc3Qgc2Vjb25kU3ltYm9sID0gc2xvdEljb25zLmxlbmd0aCA+IDEgPyBzbG90SWNvbnNbMV0gOiAnJztcclxuICAgICAgICBjb25zdCB0aGlyZFN5bWJvbCA9IHNsb3RJY29ucy5sZW5ndGggPiAyID8gc2xvdEljb25zWzJdIDogJyc7XHJcbiAgICAgICAgcmV0dXJuIFtmaXJzdFN5bWJvbCwgc2Vjb25kU3ltYm9sLCB0aGlyZFN5bWJvbF07XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgcmV0dXJuIEFycmF5KDMpLmZpbGwoc2xvdEljb25zWzBdIHx8ICcnKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZVN0YXRlPFNsb3RNYWNoaW5lU3RhdGU+KHtcclxuICAgICAgcmVlbHM6IGdldEluaXRpYWxSZWVscygpLFxyXG4gICAgICBzcGlubmluZzogZmFsc2UsXHJcbiAgICB9KTtcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICBpZiAoaW5pdGlhbFBvc2l0aW9uICE9PSBudWxsICYmICFzdGF0ZS5zcGlubmluZykge1xyXG4gICAgICAgIHNldFN0YXRlKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgIHJlZWxzOiBnZXRJbml0aWFsUmVlbHMoKSxcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH1cclxuICAgIH0sIFtpbml0aWFsUG9zaXRpb24sIHNsb3RJY29uc10pO1xyXG5cclxuICAgIGNvbnN0IGdldFJhbmRvbVN5bWJvbCA9ICgpOiBTbG90U3ltYm9sID0+IHtcclxuICAgICAgaWYgKHNsb3RJY29ucyAmJiBzbG90SWNvbnMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnN0IHJhbmRvbUluZGV4ID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogc2xvdEljb25zLmxlbmd0aCk7XHJcbiAgICAgICAgcmV0dXJuIHNsb3RJY29uc1tyYW5kb21JbmRleF07XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuICcnO1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBzcGluID0gKCkgPT4ge1xyXG4gICAgICBpZiAoc3RhdGUuc3Bpbm5pbmcpIHJldHVybjtcclxuXHJcbiAgICAgIG9uU3BpblN0YXJ0KCk7XHJcblxyXG4gICAgICBjb25zdCBzcGlubmluZ1N5bWJvbHM6IFNsb3RTeW1ib2xbXSA9IHN0YXRlLnJlZWxzLm1hcCgoKSA9PlxyXG4gICAgICAgIGdldFJhbmRvbVN5bWJvbCgpLFxyXG4gICAgICApO1xyXG5cclxuICAgICAgc2V0U3RhdGUoe1xyXG4gICAgICAgIHJlZWxzOiBzcGlubmluZ1N5bWJvbHMsXHJcbiAgICAgICAgc3Bpbm5pbmc6IHRydWUsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgbGV0IG5ld1JlZWxzOiBTbG90U3ltYm9sW107XHJcblxyXG4gICAgICBpZiAocmVzdWx0SW5kZXggPT09IDApIHtcclxuICAgICAgICBjb25zdCBzYW1lU3ltYm9sID1cclxuICAgICAgICAgIHNsb3RJY29ucyAmJiBzbG90SWNvbnMubGVuZ3RoID4gMCA/IHNsb3RJY29uc1swXSA6ICcnO1xyXG4gICAgICAgIG5ld1JlZWxzID0gW3NhbWVTeW1ib2wsIHNhbWVTeW1ib2wsIHNhbWVTeW1ib2xdO1xyXG4gICAgICB9IGVsc2UgaWYgKHJlc3VsdEluZGV4ID09PSAxKSB7XHJcbiAgICAgICAgY29uc3QgZmlyc3RTeW1ib2wgPVxyXG4gICAgICAgICAgc2xvdEljb25zICYmIHNsb3RJY29ucy5sZW5ndGggPiAwID8gc2xvdEljb25zWzBdIDogJyc7XHJcbiAgICAgICAgY29uc3Qgc2Vjb25kU3ltYm9sID1cclxuICAgICAgICAgIHNsb3RJY29ucyAmJiBzbG90SWNvbnMubGVuZ3RoID4gMSA/IHNsb3RJY29uc1sxXSA6IGZpcnN0U3ltYm9sO1xyXG4gICAgICAgIG5ld1JlZWxzID0gW2ZpcnN0U3ltYm9sLCBmaXJzdFN5bWJvbCwgc2Vjb25kU3ltYm9sXTtcclxuICAgICAgfSBlbHNlIGlmIChyZXN1bHRJbmRleCA9PT0gMikge1xyXG4gICAgICAgIGNvbnN0IGZpcnN0U3ltYm9sID1cclxuICAgICAgICAgIHNsb3RJY29ucyAmJiBzbG90SWNvbnMubGVuZ3RoID4gMCA/IHNsb3RJY29uc1swXSA6ICcnO1xyXG4gICAgICAgIGNvbnN0IHNlY29uZFN5bWJvbCA9XHJcbiAgICAgICAgICBzbG90SWNvbnMgJiYgc2xvdEljb25zLmxlbmd0aCA+IDEgPyBzbG90SWNvbnNbMV0gOiAnJztcclxuICAgICAgICBjb25zdCB0aGlyZFN5bWJvbCA9XHJcbiAgICAgICAgICBzbG90SWNvbnMgJiYgc2xvdEljb25zLmxlbmd0aCA+IDIgPyBzbG90SWNvbnNbMl0gOiAnJztcclxuICAgICAgICBuZXdSZWVscyA9IFtmaXJzdFN5bWJvbCwgc2Vjb25kU3ltYm9sLCB0aGlyZFN5bWJvbF07XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgbmV3UmVlbHMgPSBbZ2V0UmFuZG9tU3ltYm9sKCksIGdldFJhbmRvbVN5bWJvbCgpLCBnZXRSYW5kb21TeW1ib2woKV07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNldFN0YXRlKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgIHJlZWxzOiBbbmV3UmVlbHNbMF0sIHByZXYucmVlbHNbMV0sIHByZXYucmVlbHNbMl1dLFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfSwgMTUwMCk7XHJcblxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzZXRTdGF0ZSgocHJldikgPT4gKHtcclxuICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICByZWVsczogW25ld1JlZWxzWzBdLCBuZXdSZWVsc1sxXSwgcHJldi5yZWVsc1syXV0sXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICB9LCAyMDAwKTtcclxuXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNldFN0YXRlKHtcclxuICAgICAgICAgIHJlZWxzOiBuZXdSZWVscyxcclxuICAgICAgICAgIHNwaW5uaW5nOiBmYWxzZSxcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgb25TcGluRW5kKCk7XHJcbiAgICAgIH0sIDI1MDApO1xyXG4gICAgfTtcclxuXHJcbiAgICB1c2VJbXBlcmF0aXZlSGFuZGxlKHJlZiwgKCkgPT4gKHtcclxuICAgICAgc3BpbixcclxuICAgIH0pKTtcclxuXHJcbiAgICBjb25zdCByZW5kZXJTeW1ib2wgPSAoc3ltYm9sOiBTbG90U3ltYm9sKSA9PiB7XHJcbiAgICAgIHJldHVybiAoXHJcbiAgICAgICAgPGltZ1xyXG4gICAgICAgICAgc3JjPXtzeW1ib2x9XHJcbiAgICAgICAgICBhbHQ9XCJTbG90IFN5bWJvbFwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgb2JqZWN0LWNvbnRhaW5cIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICk7XHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcC04IHJvdW5kZWQteGwgc2hhZG93LWxnIG1heC13LW1kIG14LWF1dG8gYm9yZGVyLTQgYm9yZGVyLXllbGxvdy01MDBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiB3LWZ1bGxcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICB7c3RhdGUucmVlbHMubWFwKChzeW1ib2wsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctMTYgaC0xNiBib3JkZXIgYm9yZGVyLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IG92ZXJmbG93LWhpZGRlblwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtNHhsXHJcbiAgICAgICAgICAgICAgICAgICR7c3RhdGUuc3Bpbm5pbmcgPyBgYW5pbWF0ZS1zcGluLSR7aW5kZXggKyAxfWAgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7cmVuZGVyU3ltYm9sKHN5bWJvbCl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH0sXHJcbik7XHJcblxyXG5TbG90TWFjaGluZS5kaXNwbGF5TmFtZSA9ICdTbG90TWFjaGluZSc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTbG90TWFjaGluZTtcclxuIl0sIm5hbWVzIjpbImZvcndhcmRSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VJbXBlcmF0aXZlSGFuZGxlIiwidXNlU3RhdGUiLCJTbG90TWFjaGluZSIsInJld2FyZHMiLCJvblNwaW5TdGFydCIsIm9uU3BpbkVuZCIsInJlc3VsdEluZGV4Iiwic2xvdEljb25zIiwiaW5pdGlhbFBvc2l0aW9uIiwicmVmIiwic3R5bGVFbCIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImFuaW1hdGlvbnMiLCJ0ZXh0Q29udGVudCIsImpvaW4iLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJyZW1vdmVDaGlsZCIsImdldEluaXRpYWxSZWVscyIsImxlbmd0aCIsIkFycmF5IiwiZmlsbCIsInNhbWVTeW1ib2wiLCJmaXJzdFN5bWJvbCIsInNlY29uZFN5bWJvbCIsInRoaXJkU3ltYm9sIiwic3RhdGUiLCJzZXRTdGF0ZSIsInJlZWxzIiwic3Bpbm5pbmciLCJwcmV2IiwiZ2V0UmFuZG9tU3ltYm9sIiwicmFuZG9tSW5kZXgiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJzcGluIiwic3Bpbm5pbmdTeW1ib2xzIiwibWFwIiwibmV3UmVlbHMiLCJzZXRUaW1lb3V0IiwicmVuZGVyU3ltYm9sIiwic3ltYm9sIiwiaW1nIiwic3JjIiwiYWx0IiwiY2xhc3NOYW1lIiwiZGl2IiwiaW5kZXgiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/SlotMachine.tsx\n");

/***/ }),

/***/ "./components/Tasks/Luckydraw/LuckydrawSlotBody.tsx":
/*!**********************************************************!*\
  !*** ./components/Tasks/Luckydraw/LuckydrawSlotBody.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Apps/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _Components_SlotMachine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/SlotMachine */ \"./components/SlotMachine.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _luckydraw_gql__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./luckydraw.gql */ \"./components/Tasks/Luckydraw/luckydraw.gql.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_4__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_8__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_9__]);\n([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_4__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_8__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst LuckydrawPlayBody = ({ projectEventId, task, verified, onError, scoredPoints, taskParticipation, project })=>{\n    const participationResultIndex = taskParticipation?.info?.resultIndex ?? null;\n    const [isParticipating, setIsParticipating] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [wheelState, setWheelState] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({\n        isSpinning: false,\n        resultIndex: participationResultIndex\n    });\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(verified && !wheelState.isSpinning);\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const { id, description } = task;\n    const taskInfo = task.info;\n    const { rewardType, rewards } = taskInfo;\n    const rewardTypes = rewardType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.RewardType.POINTS ? `${globalT(\"projectPoints\")}` : `${globalT(\"projectXp\")}`;\n    const [participateLuckydraw] = (0,_luckydraw_gql__WEBPACK_IMPORTED_MODULE_9__.useParticipateLuckydraw)();\n    const slotMachineRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const handleSpinStart = ()=>{\n        setWheelState((prev)=>({\n                ...prev,\n                isSpinning: true\n            }));\n        setIsParticipating(true);\n        setShowResult(false);\n    };\n    const handleSpinComplete = ()=>{\n        setWheelState((prev)=>({\n                ...prev,\n                isSpinning: false\n            }));\n        setShowResult(true);\n        (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n            title: \"Quest Submitted\",\n            defaultText: \"We have verified that you performed the quest\",\n            type: \"success\"\n        });\n        setIsParticipating(false);\n    };\n    const handleClick = async ()=>{\n        if (wheelState.isSpinning) return;\n        try {\n            // First initiate the API call\n            const response = await participateLuckydraw({\n                variables: {\n                    eventId: projectEventId,\n                    taskId: id\n                },\n                context: {\n                    rewardType\n                }\n            });\n            // Update the resultIndex with the API response\n            setWheelState((prev)=>({\n                    ...prev,\n                    resultIndex: response?.data?.participateLuckydrawTask?.resultIndex ?? 0\n                }));\n            // Now trigger the slot machine spin\n            if (slotMachineRef.current) {\n                slotMachineRef.current.spin();\n            }\n        } catch (error) {\n            setWheelState((prev)=>({\n                    ...prev,\n                    isSpinning: false\n                }));\n            setIsParticipating(false);\n            (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                title: \"Error!\",\n                defaultText: \"Error spinning the wheel!\",\n                type: \"error\",\n                error\n            });\n            onError?.();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawSlotBody.tsx\",\n                lineNumber: 110,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SlotMachine__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                rewards: rewards,\n                onSpinStart: handleSpinStart,\n                onSpinEnd: handleSpinComplete,\n                resultIndex: wheelState.resultIndex,\n                slotIcons: taskInfo.slotMachineIcons,\n                initialPosition: verified && !isParticipating ? participationResultIndex : null,\n                ref: slotMachineRef\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawSlotBody.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            verified && showResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                points: scoredPoints,\n                title: `Congratulations! You won ${wheelState.resultIndex !== null ? rewards[wheelState.resultIndex].amount : \"failed to fetch\"} ${rewardTypes}.`,\n                frequency: task.frequency,\n                status: taskParticipation?.status\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawSlotBody.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                onClick: handleClick,\n                disabled: wheelState.isSpinning || verified,\n                loading: wheelState.isSpinning,\n                block: true,\n                rounded: \"full\",\n                children: wheelState.isSpinning ? \"Spinning...\" : \"Spin the Slot\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawSlotBody.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawSlotBody.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LuckydrawPlayBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL0x1Y2t5ZHJhdy9MdWNreWRyYXdTbG90Qm9keS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3dCO0FBQzJDO0FBRWpCO0FBQ0Y7QUFDRDtBQUNEO0FBQ0w7QUFDVztBQUNNO0FBTTFELE1BQU1VLG9CQUFvQixDQUFDLEVBQ3pCQyxjQUFjLEVBQ2RDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLFlBQVksRUFDWkMsaUJBQWlCLEVBQ2pCQyxPQUFPLEVBQ2dCO0lBQ3ZCLE1BQU1DLDJCQUEyQixtQkFBb0JDLE1BQXlDQyxlQUFlO0lBRTdHLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDZ0IsWUFBWUMsY0FBYyxHQUFHakIsK0NBQVFBLENBQUM7UUFDM0NrQixZQUFZO1FBQ1pMLGFBQWFGO0lBQ2Y7SUFFQSxNQUFNLENBQUNRLFlBQVlDLGNBQWMsR0FBR3BCLCtDQUFRQSxDQUMxQ00sWUFBWSxDQUFDVSxXQUFXRSxVQUFVO0lBR3BDLE1BQU0sRUFBRUcsR0FBR0MsT0FBTyxFQUFFLEdBQUd4Qiw0REFBY0EsQ0FBQyxlQUFlO1FBQUV5QixXQUFXO0lBQVM7SUFFM0UsTUFBTSxFQUFFQyxFQUFFLEVBQUVDLFdBQVcsRUFBRSxHQUFHcEI7SUFDNUIsTUFBTXFCLFdBQVdyQixLQUFLTyxJQUFJO0lBQzFCLE1BQU0sRUFBRWUsVUFBVSxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7SUFDaEMsTUFBTUcsY0FDSkYsZUFBZWxDLHNEQUFVQSxDQUFDcUMsTUFBTSxHQUM1QixDQUFDLEVBQUVSLFFBQVEsaUJBQWlCLENBQUMsR0FDN0IsQ0FBQyxFQUFFQSxRQUFRLGFBQWEsQ0FBQztJQUUvQixNQUFNLENBQUNTLHFCQUFxQixHQUFHN0IsdUVBQXVCQTtJQUN0RCxNQUFNOEIsaUJBQWlCakMsNkNBQU1BLENBQThCO0lBRTNELE1BQU1rQyxrQkFBa0I7UUFDdEJoQixjQUFjLENBQUNpQixPQUFVO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUVoQixZQUFZO1lBQUs7UUFDckRILG1CQUFtQjtRQUNuQkssY0FBYztJQUNoQjtJQUVBLE1BQU1lLHFCQUFxQjtRQUN6QmxCLGNBQWMsQ0FBQ2lCLE9BQVU7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRWhCLFlBQVk7WUFBTTtRQUN0REUsY0FBYztRQUNkbkIsbUVBQVdBLENBQUM7WUFDVm1DLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxNQUFNO1FBQ1I7UUFDQXZCLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU13QixjQUFjO1FBQ2xCLElBQUl2QixXQUFXRSxVQUFVLEVBQUU7UUFFM0IsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixNQUFNc0IsV0FBVyxNQUFNVCxxQkFBcUI7Z0JBQzFDVSxXQUFXO29CQUNUQyxTQUFTdEM7b0JBQ1R1QyxRQUFRbkI7Z0JBQ1Y7Z0JBQ0FvQixTQUFTO29CQUFFakI7Z0JBQVc7WUFDeEI7WUFFQSwrQ0FBK0M7WUFDL0NWLGNBQWMsQ0FBQ2lCLE9BQVU7b0JBQ3ZCLEdBQUdBLElBQUk7b0JBQ1ByQixhQUFhMkIsVUFBVUssTUFBTUMsMEJBQTBCakMsZUFBZTtnQkFDeEU7WUFFQSxvQ0FBb0M7WUFDcEMsSUFBSW1CLGVBQWVlLE9BQU8sRUFBRTtnQkFDMUJmLGVBQWVlLE9BQU8sQ0FBQ0MsSUFBSTtZQUM3QjtRQUNGLEVBQUUsT0FBT0MsT0FBWTtZQUNuQmhDLGNBQWMsQ0FBQ2lCLE9BQVU7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRWhCLFlBQVk7Z0JBQU07WUFDdERILG1CQUFtQjtZQUNuQmQsbUVBQVdBLENBQUM7Z0JBQ1ZtQyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxNQUFNO2dCQUNOVztZQUNGO1lBQ0ExQztRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzJDO1FBQUlDLFdBQVU7O1lBQ1oxQiw2QkFBZSw4REFBQzdCLDhEQUFVQTtnQkFBQ3dELGNBQWMzQjs7Ozs7OzBCQUMxQyw4REFBQzlCLCtEQUFXQTtnQkFDVmlDLFNBQVNBO2dCQUNUeUIsYUFBYXBCO2dCQUNicUIsV0FBV25CO2dCQUNYdEIsYUFBYUcsV0FBV0gsV0FBVztnQkFDbkMwQyxXQUFXN0IsU0FBUzhCLGdCQUFnQjtnQkFDcENDLGlCQUFpQm5ELFlBQVksQ0FBQ1Esa0JBQWtCSCwyQkFBMkI7Z0JBQzNFK0MsS0FBSzFCOzs7Ozs7WUFFTjFCLFlBQVlhLDJCQUNYLDhEQUFDekIsMEVBQWlCQTtnQkFDaEJpRSxRQUFRbkQ7Z0JBQ1I0QixPQUFPLENBQUMseUJBQXlCLEVBQy9CcEIsV0FBV0gsV0FBVyxLQUFLLE9BQ3ZCZSxPQUFPLENBQUNaLFdBQVdILFdBQVcsQ0FBQyxDQUFDK0MsTUFBTSxHQUN0QyxrQkFDTCxDQUFDLEVBQUUvQixZQUFZLENBQUMsQ0FBQztnQkFDbEJnQyxXQUFXeEQsS0FBS3dELFNBQVM7Z0JBQ3pCQyxRQUFRckQsbUJBQW1CcUQ7Ozs7OzBDQUc3Qiw4REFBQ2pFLHlEQUFNQTtnQkFDTGtFLFNBQVN4QjtnQkFDVHlCLFVBQVVoRCxXQUFXRSxVQUFVLElBQUlaO2dCQUNuQzJELFNBQVNqRCxXQUFXRSxVQUFVO2dCQUM5QmdELE9BQU87Z0JBQ1BDLFNBQVE7MEJBRVBuRCxXQUFXRSxVQUFVLEdBQUcsZ0JBQWdCOzs7Ozs7Ozs7Ozs7QUFLbkQ7QUFFQSxpRUFBZWYsaUJBQWlCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9UYXNrcy9MdWNreWRyYXcvTHVja3lkcmF3U2xvdEJvZHkudHN4PzMzOWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBMdWNreWRyYXdUYXNrRGF0YSxcclxuICBMdWNreWRyYXdUYXNrUGFydGljaXBhdGlvbkRhdGEsXHJcbiAgUHJvamVjdCxcclxuICBSZXdhcmRUeXBlLFxyXG59IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuaW1wb3J0IFRhc2tDb21wbGV0ZWRDYXJkIGZyb20gJ0BBcHBzL2NvbXBvbmVudHMvVGFza0NvbXBsZXRlZENhcmQnO1xyXG5pbXBvcnQgeyBUYXNrQm9keVByb3BzIH0gZnJvbSAnQEFwcHMvVGFza0xpc3RJdGVtRXhwYW5kZWQnO1xyXG5pbXBvcnQgU2xvdE1hY2hpbmUgZnJvbSAnQENvbXBvbmVudHMvU2xvdE1hY2hpbmUnO1xyXG5pbXBvcnQgVGV4dEVkaXRvciBmcm9tICdAQ29tcG9uZW50cy9UZXh0RWRpdG9yJztcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQENvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5pbXBvcnQgeyB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgdGFza1RvYXN0ZXIgZnJvbSAnLi4vY29tcG9uZW50cy9UYXNrVG9hc3Rlcic7XHJcbmltcG9ydCB7IHVzZVBhcnRpY2lwYXRlTHVja3lkcmF3IH0gZnJvbSAnLi9sdWNreWRyYXcuZ3FsJztcclxuXHJcbmludGVyZmFjZSBMdWNreWRyYXdQbGF5Qm9keVByb3BzIGV4dGVuZHMgVGFza0JvZHlQcm9wcyB7XHJcbiAgcHJvamVjdDogUHJvamVjdDtcclxufVxyXG5cclxuY29uc3QgTHVja3lkcmF3UGxheUJvZHkgPSAoe1xyXG4gIHByb2plY3RFdmVudElkLFxyXG4gIHRhc2ssXHJcbiAgdmVyaWZpZWQsXHJcbiAgb25FcnJvcixcclxuICBzY29yZWRQb2ludHMsXHJcbiAgdGFza1BhcnRpY2lwYXRpb24sXHJcbiAgcHJvamVjdCxcclxufTogTHVja3lkcmF3UGxheUJvZHlQcm9wcykgPT4ge1xyXG4gIGNvbnN0IHBhcnRpY2lwYXRpb25SZXN1bHRJbmRleCA9ICh0YXNrUGFydGljaXBhdGlvbj8uaW5mbyBhcyBMdWNreWRyYXdUYXNrUGFydGljaXBhdGlvbkRhdGEpPy5yZXN1bHRJbmRleCA/PyBudWxsO1xyXG5cclxuICBjb25zdCBbaXNQYXJ0aWNpcGF0aW5nLCBzZXRJc1BhcnRpY2lwYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFt3aGVlbFN0YXRlLCBzZXRXaGVlbFN0YXRlXSA9IHVzZVN0YXRlKHtcclxuICAgIGlzU3Bpbm5pbmc6IGZhbHNlLFxyXG4gICAgcmVzdWx0SW5kZXg6IHBhcnRpY2lwYXRpb25SZXN1bHRJbmRleCxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW3Nob3dSZXN1bHQsIHNldFNob3dSZXN1bHRdID0gdXNlU3RhdGUoXHJcbiAgICB2ZXJpZmllZCAmJiAhd2hlZWxTdGF0ZS5pc1NwaW5uaW5nLFxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHsgdDogZ2xvYmFsVCB9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJywgeyBrZXlQcmVmaXg6ICdnbG9iYWwnIH0pO1xyXG5cclxuICBjb25zdCB7IGlkLCBkZXNjcmlwdGlvbiB9ID0gdGFzaztcclxuICBjb25zdCB0YXNrSW5mbyA9IHRhc2suaW5mbyBhcyBMdWNreWRyYXdUYXNrRGF0YTtcclxuICBjb25zdCB7IHJld2FyZFR5cGUsIHJld2FyZHMgfSA9IHRhc2tJbmZvO1xyXG4gIGNvbnN0IHJld2FyZFR5cGVzID1cclxuICAgIHJld2FyZFR5cGUgPT09IFJld2FyZFR5cGUuUE9JTlRTXHJcbiAgICAgID8gYCR7Z2xvYmFsVCgncHJvamVjdFBvaW50cycpfWBcclxuICAgICAgOiBgJHtnbG9iYWxUKCdwcm9qZWN0WHAnKX1gO1xyXG5cclxuICBjb25zdCBbcGFydGljaXBhdGVMdWNreWRyYXddID0gdXNlUGFydGljaXBhdGVMdWNreWRyYXcoKTtcclxuICBjb25zdCBzbG90TWFjaGluZVJlZiA9IHVzZVJlZjx7IHNwaW46ICgpID0+IHZvaWQgfSB8IG51bGw+KG51bGwpO1xyXG5cclxuICBjb25zdCBoYW5kbGVTcGluU3RhcnQgPSAoKSA9PiB7XHJcbiAgICBzZXRXaGVlbFN0YXRlKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBpc1NwaW5uaW5nOiB0cnVlIH0pKTtcclxuICAgIHNldElzUGFydGljaXBhdGluZyh0cnVlKTtcclxuICAgIHNldFNob3dSZXN1bHQoZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNwaW5Db21wbGV0ZSA9ICgpID0+IHtcclxuICAgIHNldFdoZWVsU3RhdGUoKHByZXYpID0+ICh7IC4uLnByZXYsIGlzU3Bpbm5pbmc6IGZhbHNlIH0pKTtcclxuICAgIHNldFNob3dSZXN1bHQodHJ1ZSk7XHJcbiAgICB0YXNrVG9hc3Rlcih7XHJcbiAgICAgIHRpdGxlOiAnUXVlc3QgU3VibWl0dGVkJyxcclxuICAgICAgZGVmYXVsdFRleHQ6ICdXZSBoYXZlIHZlcmlmaWVkIHRoYXQgeW91IHBlcmZvcm1lZCB0aGUgcXVlc3QnLFxyXG4gICAgICB0eXBlOiAnc3VjY2VzcycsXHJcbiAgICB9KTtcclxuICAgIHNldElzUGFydGljaXBhdGluZyhmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAod2hlZWxTdGF0ZS5pc1NwaW5uaW5nKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gRmlyc3QgaW5pdGlhdGUgdGhlIEFQSSBjYWxsXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcGFydGljaXBhdGVMdWNreWRyYXcoe1xyXG4gICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgZXZlbnRJZDogcHJvamVjdEV2ZW50SWQsXHJcbiAgICAgICAgICB0YXNrSWQ6IGlkLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY29udGV4dDogeyByZXdhcmRUeXBlIH0sXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gVXBkYXRlIHRoZSByZXN1bHRJbmRleCB3aXRoIHRoZSBBUEkgcmVzcG9uc2VcclxuICAgICAgc2V0V2hlZWxTdGF0ZSgocHJldikgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIHJlc3VsdEluZGV4OiByZXNwb25zZT8uZGF0YT8ucGFydGljaXBhdGVMdWNreWRyYXdUYXNrPy5yZXN1bHRJbmRleCA/PyAwLFxyXG4gICAgICB9KSk7XHJcblxyXG4gICAgICAvLyBOb3cgdHJpZ2dlciB0aGUgc2xvdCBtYWNoaW5lIHNwaW5cclxuICAgICAgaWYgKHNsb3RNYWNoaW5lUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzbG90TWFjaGluZVJlZi5jdXJyZW50LnNwaW4oKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBzZXRXaGVlbFN0YXRlKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBpc1NwaW5uaW5nOiBmYWxzZSB9KSk7XHJcbiAgICAgIHNldElzUGFydGljaXBhdGluZyhmYWxzZSk7XHJcbiAgICAgIHRhc2tUb2FzdGVyKHtcclxuICAgICAgICB0aXRsZTogJ0Vycm9yIScsXHJcbiAgICAgICAgZGVmYXVsdFRleHQ6ICdFcnJvciBzcGlubmluZyB0aGUgd2hlZWwhJyxcclxuICAgICAgICB0eXBlOiAnZXJyb3InLFxyXG4gICAgICAgIGVycm9yLFxyXG4gICAgICB9KTtcclxuICAgICAgb25FcnJvcj8uKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgIHtkZXNjcmlwdGlvbiAmJiA8VGV4dEVkaXRvciBkZWZhdWx0VmFsdWU9e2Rlc2NyaXB0aW9ufSAvPn1cclxuICAgICAgPFNsb3RNYWNoaW5lXHJcbiAgICAgICAgcmV3YXJkcz17cmV3YXJkc31cclxuICAgICAgICBvblNwaW5TdGFydD17aGFuZGxlU3BpblN0YXJ0fVxyXG4gICAgICAgIG9uU3BpbkVuZD17aGFuZGxlU3BpbkNvbXBsZXRlfVxyXG4gICAgICAgIHJlc3VsdEluZGV4PXt3aGVlbFN0YXRlLnJlc3VsdEluZGV4fVxyXG4gICAgICAgIHNsb3RJY29ucz17dGFza0luZm8uc2xvdE1hY2hpbmVJY29uc31cclxuICAgICAgICBpbml0aWFsUG9zaXRpb249e3ZlcmlmaWVkICYmICFpc1BhcnRpY2lwYXRpbmcgPyBwYXJ0aWNpcGF0aW9uUmVzdWx0SW5kZXggOiBudWxsfVxyXG4gICAgICAgIHJlZj17c2xvdE1hY2hpbmVSZWZ9XHJcbiAgICAgIC8+XHJcbiAgICAgIHt2ZXJpZmllZCAmJiBzaG93UmVzdWx0ID8gKFxyXG4gICAgICAgIDxUYXNrQ29tcGxldGVkQ2FyZFxyXG4gICAgICAgICAgcG9pbnRzPXtzY29yZWRQb2ludHN9XHJcbiAgICAgICAgICB0aXRsZT17YENvbmdyYXR1bGF0aW9ucyEgWW91IHdvbiAke1xyXG4gICAgICAgICAgICB3aGVlbFN0YXRlLnJlc3VsdEluZGV4ICE9PSBudWxsXHJcbiAgICAgICAgICAgICAgPyByZXdhcmRzW3doZWVsU3RhdGUucmVzdWx0SW5kZXhdLmFtb3VudFxyXG4gICAgICAgICAgICAgIDogJ2ZhaWxlZCB0byBmZXRjaCdcclxuICAgICAgICAgIH0gJHtyZXdhcmRUeXBlc30uYH1cclxuICAgICAgICAgIGZyZXF1ZW5jeT17dGFzay5mcmVxdWVuY3l9XHJcbiAgICAgICAgICBzdGF0dXM9e3Rhc2tQYXJ0aWNpcGF0aW9uPy5zdGF0dXN9XHJcbiAgICAgICAgLz5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cclxuICAgICAgICAgIGRpc2FibGVkPXt3aGVlbFN0YXRlLmlzU3Bpbm5pbmcgfHwgdmVyaWZpZWR9XHJcbiAgICAgICAgICBsb2FkaW5nPXt3aGVlbFN0YXRlLmlzU3Bpbm5pbmd9XHJcbiAgICAgICAgICBibG9jaz17dHJ1ZX1cclxuICAgICAgICAgIHJvdW5kZWQ9XCJmdWxsXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICB7d2hlZWxTdGF0ZS5pc1NwaW5uaW5nID8gJ1NwaW5uaW5nLi4uJyA6ICdTcGluIHRoZSBTbG90J31cclxuICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMdWNreWRyYXdQbGF5Qm9keTtcclxuIl0sIm5hbWVzIjpbIlJld2FyZFR5cGUiLCJUYXNrQ29tcGxldGVkQ2FyZCIsIlNsb3RNYWNoaW5lIiwiVGV4dEVkaXRvciIsIkJ1dHRvbiIsInVzZVRyYW5zbGF0aW9uIiwidXNlUmVmIiwidXNlU3RhdGUiLCJ0YXNrVG9hc3RlciIsInVzZVBhcnRpY2lwYXRlTHVja3lkcmF3IiwiTHVja3lkcmF3UGxheUJvZHkiLCJwcm9qZWN0RXZlbnRJZCIsInRhc2siLCJ2ZXJpZmllZCIsIm9uRXJyb3IiLCJzY29yZWRQb2ludHMiLCJ0YXNrUGFydGljaXBhdGlvbiIsInByb2plY3QiLCJwYXJ0aWNpcGF0aW9uUmVzdWx0SW5kZXgiLCJpbmZvIiwicmVzdWx0SW5kZXgiLCJpc1BhcnRpY2lwYXRpbmciLCJzZXRJc1BhcnRpY2lwYXRpbmciLCJ3aGVlbFN0YXRlIiwic2V0V2hlZWxTdGF0ZSIsImlzU3Bpbm5pbmciLCJzaG93UmVzdWx0Iiwic2V0U2hvd1Jlc3VsdCIsInQiLCJnbG9iYWxUIiwia2V5UHJlZml4IiwiaWQiLCJkZXNjcmlwdGlvbiIsInRhc2tJbmZvIiwicmV3YXJkVHlwZSIsInJld2FyZHMiLCJyZXdhcmRUeXBlcyIsIlBPSU5UUyIsInBhcnRpY2lwYXRlTHVja3lkcmF3Iiwic2xvdE1hY2hpbmVSZWYiLCJoYW5kbGVTcGluU3RhcnQiLCJwcmV2IiwiaGFuZGxlU3BpbkNvbXBsZXRlIiwidGl0bGUiLCJkZWZhdWx0VGV4dCIsInR5cGUiLCJoYW5kbGVDbGljayIsInJlc3BvbnNlIiwidmFyaWFibGVzIiwiZXZlbnRJZCIsInRhc2tJZCIsImNvbnRleHQiLCJkYXRhIiwicGFydGljaXBhdGVMdWNreWRyYXdUYXNrIiwiY3VycmVudCIsInNwaW4iLCJlcnJvciIsImRpdiIsImNsYXNzTmFtZSIsImRlZmF1bHRWYWx1ZSIsIm9uU3BpblN0YXJ0Iiwib25TcGluRW5kIiwic2xvdEljb25zIiwic2xvdE1hY2hpbmVJY29ucyIsImluaXRpYWxQb3NpdGlvbiIsInJlZiIsInBvaW50cyIsImFtb3VudCIsImZyZXF1ZW5jeSIsInN0YXR1cyIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImxvYWRpbmciLCJibG9jayIsInJvdW5kZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Tasks/Luckydraw/LuckydrawSlotBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MediaDisplay({ block, contentState }) {\n    const entityKey = block.getEntityAt(0);\n    if (!entityKey) return null;\n    const entity = contentState.getEntity(entityKey);\n    const { src, mediaType } = entity.getData();\n    if (!src) return null;\n    if (mediaType === \"video\" || entity.getType() === \"VIDEO\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n            controls: true,\n            preload: \"metadata\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\",\n                borderRadius: \"6px\",\n                margin: \"8px 0\"\n            },\n            src: src,\n            children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: \"Rich Text Image\",\n        style: {\n            maxWidth: \"100%\",\n            height: \"auto\",\n            borderRadius: \"6px\",\n            margin: \"8px 0\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction mediaBlockRenderer(block) {\n    if (block.getType() === \"atomic\") {\n        return {\n            component: MediaDisplay,\n            editable: false\n        };\n    }\n    return null;\n}\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_4__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_4__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_4__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                className: \"jsx-7a4dfc642d828fbd\" + \" \" + ((0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\") || \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"7a4dfc642d828fbd\",\n                        children: \".RichEditor-text-align-left,.RichEditor-text-align-left .public-DraftStyleDefault-block{text-align:left!important}.RichEditor-text-align-center,.RichEditor-text-align-center .public-DraftStyleDefault-block{text-align:center!important}.RichEditor-text-align-right,.RichEditor-text-align-right .public-DraftStyleDefault-block{text-align:right!important}\"\n                    }, void 0, false, void 0, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_4__.Editor, {\n                        editorState: editorState,\n                        readOnly: readonly,\n                        onChange: ()=>{},\n                        blockRendererFn: mediaBlockRenderer,\n                        blockStyleFn: (block)=>{\n                            const blockData = block.getData();\n                            const textAlign = blockData.get(\"textAlign\") || \"left\";\n                            let className = \"\";\n                            switch(block.getType()){\n                                case \"blockquote\":\n                                    className = \"RichEditor-blockquote\";\n                                    break;\n                                default:\n                                    className = \"\";\n                            }\n                            className += ` RichEditor-text-align-${textAlign}`;\n                            return className.trim();\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;