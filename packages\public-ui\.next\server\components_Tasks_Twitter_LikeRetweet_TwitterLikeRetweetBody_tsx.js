"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Twitter_LikeRetweet_TwitterLikeRetweetBody_tsx";
exports.ids = ["components_Tasks_Twitter_LikeRetweet_TwitterLikeRetweetBody_tsx"];
exports.modules = {

/***/ "./components/Loaders/SocialPostLoader.tsx":
/*!*************************************************!*\
  !*** ./components/Loaders/SocialPostLoader.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-content-loader */ \"react-content-loader\");\n/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_content_loader__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SocialPostLoader = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_content_loader__WEBPACK_IMPORTED_MODULE_1___default()), {\n        speed: 2,\n        width: \"100%\",\n        height: 460,\n        viewBox: \"0 0 400 460\",\n        backgroundColor: \"var(--skeleton-background)\",\n        foregroundColor: \"var(--skeleton-foreground)\",\n        uniqueKey: \"social-post-loader\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n            x: \"0\",\n            y: \"0\",\n            rx: \"4\",\n            ry: \"4\",\n            width: \"100%\",\n            height: \"400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SocialPostLoader.tsx\",\n            lineNumber: 14,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Loaders\\\\SocialPostLoader.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocialPostLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRlcnMvU29jaWFsUG9zdExvYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlEO0FBRWpELE1BQU1DLG1CQUFtQixDQUFDQyxzQkFDeEIsOERBQUNGLDZEQUFhQTtRQUNaRyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxTQUFRO1FBQ1JDLGlCQUFnQjtRQUNoQkMsaUJBQWdCO1FBQ2hCQyxXQUFVO1FBQ1QsR0FBR1AsS0FBSztrQkFFVCw0RUFBQ1E7WUFBS0MsR0FBRTtZQUFJQyxHQUFFO1lBQUlDLElBQUc7WUFBSUMsSUFBRztZQUFJVixPQUFNO1lBQU9DLFFBQU87Ozs7Ozs7Ozs7O0FBSXhELGlFQUFlSixnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL0xvYWRlcnMvU29jaWFsUG9zdExvYWRlci50c3g/YTkyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29udGVudExvYWRlciBmcm9tICdyZWFjdC1jb250ZW50LWxvYWRlcic7XHJcblxyXG5jb25zdCBTb2NpYWxQb3N0TG9hZGVyID0gKHByb3BzOiBhbnkpID0+IChcclxuICA8Q29udGVudExvYWRlclxyXG4gICAgc3BlZWQ9ezJ9XHJcbiAgICB3aWR0aD17JzEwMCUnfVxyXG4gICAgaGVpZ2h0PXs0NjB9XHJcbiAgICB2aWV3Qm94PVwiMCAwIDQwMCA0NjBcIlxyXG4gICAgYmFja2dyb3VuZENvbG9yPVwidmFyKC0tc2tlbGV0b24tYmFja2dyb3VuZClcIlxyXG4gICAgZm9yZWdyb3VuZENvbG9yPVwidmFyKC0tc2tlbGV0b24tZm9yZWdyb3VuZClcIlxyXG4gICAgdW5pcXVlS2V5PVwic29jaWFsLXBvc3QtbG9hZGVyXCJcclxuICAgIHsuLi5wcm9wc31cclxuICA+XHJcbiAgICA8cmVjdCB4PVwiMFwiIHk9XCIwXCIgcng9XCI0XCIgcnk9XCI0XCIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PVwiNDAwXCIgLz5cclxuICA8L0NvbnRlbnRMb2FkZXI+XHJcbik7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTb2NpYWxQb3N0TG9hZGVyO1xyXG4iXSwibmFtZXMiOlsiQ29udGVudExvYWRlciIsIlNvY2lhbFBvc3RMb2FkZXIiLCJwcm9wcyIsInNwZWVkIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiYmFja2dyb3VuZENvbG9yIiwiZm9yZWdyb3VuZENvbG9yIiwidW5pcXVlS2V5IiwicmVjdCIsIngiLCJ5IiwicngiLCJyeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Loaders/SocialPostLoader.tsx\n");

/***/ }),

/***/ "./components/Tasks/Provider/ProviderButton.tsx":
/*!******************************************************!*\
  !*** ./components/Tasks/Provider/ProviderButton.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProviderButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useUserEventConnection */ \"./hooks/useUserEventConnection.ts\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__]);\n([_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__, _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__, _hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction ProviderButton({ projectEventId, providerType, providerData, buttonText = \"Verify using\", ...props }) {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const { connection, loading } = (0,_Hooks_useUserEventConnection__WEBPACK_IMPORTED_MODULE_3__.useUserEventConnection)(projectEventId, providerType);\n    const { unsetPreferredEventConnection } = (0,_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_6__.usePreferredEventConnection)();\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n        ...props,\n        loading: true,\n        children: \"Initializing...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 39,\n        columnNumber: 7\n    }, this);\n    const buttonTpl = ()=>{\n        if (!connection) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \" Verify \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 14\n            }, this);\n        }\n        const buttonTextTpl = ()=>{\n            switch(providerType){\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.EVM_BLOCKCHAIN:\n                    return (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.shortenAddress)(connection.providerId, 8);\n                case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AuthProvider.DOTSAMA_BLOCKCHAIN:\n                    return (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.formatDotsamaAuth)(connection, providerData.blockchainId);\n                default:\n                    return connection.username || connection.firstName || connection.providerId;\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-2 items-center\",\n            children: [\n                connection.picture && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: connection.picture,\n                    className: \"h-8 w-8 flex-shrink-0 border-2 border-white rounded-full\",\n                    alt: \"profile image\",\n                    onError: ()=>setError(true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: [\n                        buttonText,\n                        \" \",\n                        buttonTextTpl()\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                ...props,\n                children: buttonTpl()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            connection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-primary text-sm cursor-pointer text-center\",\n                onClick: ()=>{\n                    unsetPreferredEventConnection(projectEventId, providerType);\n                },\n                children: \"Use another account\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Provider\\\\ProviderButton.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Provider/ProviderButton.tsx\n");

/***/ }),

/***/ "./components/Tasks/Twitter/LikeRetweet/TwitterLikeRetweetBody.tsx":
/*!*************************************************************************!*\
  !*** ./components/Tasks/Twitter/LikeRetweet/TwitterLikeRetweetBody.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Loaders_SocialPostLoader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Loaders/SocialPostLoader */ \"./components/Loaders/SocialPostLoader.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Components_TimeLine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TimeLine */ \"./components/TimeLine/index.tsx\");\n/* harmony import */ var _Components_TweetEmbed__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TweetEmbed */ \"./components/TweetEmbed.tsx\");\n/* harmony import */ var _twitter_helper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../twitter-helper */ \"./components/Tasks/Twitter/twitter-helper.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @Components/Tasks/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Components/Tasks/Provider/ProviderButton */ \"./components/Tasks/Provider/ProviderButton.tsx\");\n/* harmony import */ var _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Components/TimeLine/TimeLineItem */ \"./components/TimeLine/TimeLineItem.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _Root_constants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @Root/constants */ \"./constants/index.ts\");\n/* harmony import */ var _gql_twitter_like_retweet_gql__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../gql/twitter-like-retweet.gql */ \"./components/Tasks/Twitter/gql/twitter-like-retweet.gql.ts\");\n/* harmony import */ var _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/Tasks/hooks/usePreferredEventConnection */ \"./components/Tasks/hooks/usePreferredEventConnection.ts\");\n/* harmony import */ var _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Components/Tasks/components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Root_config_config_json__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @Root/config/config.json */ \"./config/config.json\");\n/* harmony import */ var _Components_Button__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @Components/Button */ \"./components/Button.tsx\");\n/* harmony import */ var _TweetIntentButton__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../TweetIntentButton */ \"./components/Tasks/Twitter/TweetIntentButton.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_18__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_7__, _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_8__, _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_9__, _gql_twitter_like_retweet_gql__WEBPACK_IMPORTED_MODULE_12__, _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_13__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_14__, _Components_Button__WEBPACK_IMPORTED_MODULE_16__]);\n([_Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_7__, _Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_8__, _Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_9__, _gql_twitter_like_retweet_gql__WEBPACK_IMPORTED_MODULE_12__, _Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_13__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_14__, _Components_Button__WEBPACK_IMPORTED_MODULE_16__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst isReadOnly = _Root_config_config_json__WEBPACK_IMPORTED_MODULE_15__.root.features.includes(\"TWITTER_READONLY\");\nconst TwitterLikeRetweetBody = ({ projectEventId, task, verified, scoredPoints, onSuccess, onError, onClose, taskParticipation })=>{\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_18__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(verified ? _Root_constants__WEBPACK_IMPORTED_MODULE_11__.MAX_TIMELINE_STEPS : 0);\n    const [participateLike, { loading: verifying }] = (0,_gql_twitter_like_retweet_gql__WEBPACK_IMPORTED_MODULE_12__.useParticipateTwitterLikeRetweet)();\n    const { getPreferredEventConnection } = (0,_Components_Tasks_hooks_usePreferredEventConnection__WEBPACK_IMPORTED_MODULE_13__.usePreferredEventConnection)();\n    const providerId = getPreferredEventConnection(projectEventId, _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.AuthProvider.TWITTER) || \"\";\n    const { points, description, id } = task;\n    const { url, actions } = task.info;\n    const [likeIntentClicked, setLikeIntentClicked] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [retweetIntentClicked, setRetweetIntentClicked] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const hasLike = actions.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_6__.TwitterAction.LIKE);\n    const hasRt = actions.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_6__.TwitterAction.RETWEET);\n    const disabled = hasLike && !likeIntentClicked || hasRt && !retweetIntentClicked;\n    const tweetId = (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_5__.tweetIdFromUrl)(url);\n    const getLikeRtText = ()=>{\n        return actions.map((txt)=>txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()).join(\" & \");\n    };\n    const handleLikeRetweetVerifyClick = ()=>{\n        participateLike({\n            variables: {\n                eventId: projectEventId,\n                taskId: id,\n                providerId\n            },\n            context: {\n                points: task.points\n            },\n            onCompleted: ()=>{\n                if (isReadOnly) {\n                    onSuccess?.(\"We might take 10-20 minutes to verify your submission. Please check back for your status.\", \"Submitted for verification\", true);\n                } else {\n                    onSuccess?.();\n                }\n            },\n            onError: (error)=>{\n                (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_14__[\"default\"])({\n                    title: \"Verification Failed\",\n                    defaultText: `We were not able to verify that you have liked the item, so please make sure you have liked from your linked account & verify again.`,\n                    type: \"error\",\n                    error\n                });\n                onError?.();\n            }\n        });\n    };\n    const taskVerificationCard = ()=>{\n        if (taskParticipation?.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.ParticipationStatus.IN_REVIEW) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        points: points,\n                        title: \"We are verifying your action\",\n                        subTitle: \"Verifying your retweet/like action might take 10-20 minutes. We'll send you a notification when there is an update.\",\n                        status: taskParticipation?.status\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Button__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"mt-1\",\n                        rounded: \"full\",\n                        block: true,\n                        onClick: ()=>onClose?.(),\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            points: scoredPoints,\n            frequency: task.frequency,\n            status: taskParticipation?.status\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, undefined);\n    };\n    const intentButtonTpl = ()=>{\n        if (isReadOnly) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    actions.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_6__.TwitterAction.LIKE) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TweetIntentButton__WEBPACK_IMPORTED_MODULE_17__.TweetIntentButton, {\n                        text: \"Open Twitter to Like\",\n                        onClick: ()=>{\n                            (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_5__.handleLikeClick)(tweetId);\n                            setCurrentStep(1);\n                            setLikeIntentClicked(true);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined),\n                    actions.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_6__.TwitterAction.RETWEET) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TweetIntentButton__WEBPACK_IMPORTED_MODULE_17__.TweetIntentButton, {\n                        text: \"Open Twitter to Retweet\",\n                        onClick: ()=>{\n                            (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_5__.handleRetweetClick)(tweetId);\n                            setCurrentStep(1);\n                            setRetweetIntentClicked(true);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: actions.includes(_airlyft_types__WEBPACK_IMPORTED_MODULE_6__.TwitterAction.LIKE) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TweetIntentButton__WEBPACK_IMPORTED_MODULE_17__.TweetIntentButton, {\n                onClick: ()=>{\n                    (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_5__.handleLikeClick)(tweetId);\n                    setCurrentStep(1);\n                },\n                text: `Open Twitter to ${getLikeRtText()}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 166,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TweetIntentButton__WEBPACK_IMPORTED_MODULE_17__.TweetIntentButton, {\n                onClick: ()=>{\n                    (0,_twitter_helper__WEBPACK_IMPORTED_MODULE_5__.handleRetweetClick)(tweetId);\n                    setCurrentStep(1);\n                },\n                text: \"Open Twitter to Retweet\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 174,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false);\n    };\n    const verifyButtonText = isReadOnly ? \"Verify using\" : `${getLikeRtText()} using`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 192,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TimeLine__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                data: [\n                    {\n                        title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \" \",\n                                getLikeRtText(),\n                                \" this tweet on twitter \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 20\n                        }, void 0),\n                        extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TweetEmbed__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    tweetId: tweetId,\n                                    placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_SocialPostLoader__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 32\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, void 0),\n                                intentButtonTpl()\n                            ]\n                        }, void 0, true)\n                    },\n                    {\n                        title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                getLikeRtText(),\n                                \" via \",\n                                globalT(\"platform\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 15\n                        }, void 0),\n                        extra: verified ? taskVerificationCard() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_Provider_ProviderButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            disabled: isReadOnly ? disabled : false,\n                            providerType: _airlyft_types__WEBPACK_IMPORTED_MODULE_6__.AuthProvider.TWITTER,\n                            projectEventId: projectEventId,\n                            buttonText: verifyButtonText,\n                            rounded: \"full\",\n                            block: true,\n                            onClick: (e)=>{\n                                handleLikeRetweetVerifyClick();\n                            },\n                            loading: verifying,\n                            children: \"Verify\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 15\n                        }, void 0)\n                    }\n                ],\n                render: (item, key, status)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TimeLine_TimeLineItem__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        title: item.title,\n                        status: status,\n                        icon: key + 1,\n                        children: item.extra\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                currentStep: currentStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\LikeRetweet\\\\TwitterLikeRetweetBody.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwitterLikeRetweetBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Twitter/LikeRetweet/TwitterLikeRetweetBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/Twitter/TweetIntentButton.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/Twitter/TweetIntentButton.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetIntentButton: () => (/* binding */ TweetIntentButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_SocialIcons_Twitter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/SocialIcons/Twitter */ \"./components/SocialIcons/Twitter.tsx\");\n\n\n\nfunction TweetIntentButton({ onClick, text }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"modal-clickable-list-item-rounded\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SocialIcons_Twitter__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                color: \"#1DA1F2\",\n                className: \"flex-shrink-0 h-5 w-5 mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\TweetIntentButton.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-1 text-left font-semibold\",\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\TweetIntentButton.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_1__.ExternalLinkIcon, {\n                className: \"h-5 w-5 text-gray-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\TweetIntentButton.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Twitter\\\\TweetIntentButton.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL1R3aXR0ZXIvVHdlZXRJbnRlbnRCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEQ7QUFDQTtBQUVuRCxTQUFTRSxrQkFBa0IsRUFDaENDLE9BQU8sRUFDUEMsSUFBSSxFQUlMO0lBQ0MscUJBQ0UsOERBQUNDO1FBQU9GLFNBQVNBO1FBQVNHLFdBQVU7OzBCQUNsQyw4REFBQ0wsdUVBQVdBO2dCQUFDTSxPQUFNO2dCQUFVRCxXQUFVOzs7Ozs7MEJBQ3ZDLDhEQUFDRTtnQkFBS0YsV0FBVTswQkFBa0NGOzs7Ozs7MEJBQ2xELDhEQUFDSixvRUFBZ0JBO2dCQUFDTSxXQUFVOzs7Ozs7Ozs7Ozs7QUFHbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1Rhc2tzL1R3aXR0ZXIvVHdlZXRJbnRlbnRCdXR0b24udHN4PzRmODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRXh0ZXJuYWxMaW5rSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3Qvc29saWQnO1xyXG5pbXBvcnQgVHdpdHRlckljb24gZnJvbSAnQENvbXBvbmVudHMvU29jaWFsSWNvbnMvVHdpdHRlcic7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gVHdlZXRJbnRlbnRCdXR0b24oe1xyXG4gIG9uQ2xpY2ssXHJcbiAgdGV4dCxcclxufToge1xyXG4gIG9uQ2xpY2s6ICgpID0+IHZvaWQ7XHJcbiAgdGV4dDogc3RyaW5nO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxidXR0b24gb25DbGljaz17b25DbGlja30gY2xhc3NOYW1lPVwibW9kYWwtY2xpY2thYmxlLWxpc3QtaXRlbS1yb3VuZGVkXCI+XHJcbiAgICAgIDxUd2l0dGVySWNvbiBjb2xvcj1cIiMxREExRjJcIiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtNSB3LTUgbXItMlwiIC8+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWxlZnQgZm9udC1zZW1pYm9sZFwiPnt0ZXh0fTwvc3Bhbj5cclxuICAgICAgPEV4dGVybmFsTGlua0ljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyYXktNjAwXCIgLz5cclxuICAgIDwvYnV0dG9uPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkV4dGVybmFsTGlua0ljb24iLCJUd2l0dGVySWNvbiIsIlR3ZWV0SW50ZW50QnV0dG9uIiwib25DbGljayIsInRleHQiLCJidXR0b24iLCJjbGFzc05hbWUiLCJjb2xvciIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Tasks/Twitter/TweetIntentButton.tsx\n");

/***/ }),

/***/ "./components/Tasks/Twitter/twitter-helper.ts":
/*!****************************************************!*\
  !*** ./components/Tasks/Twitter/twitter-helper.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TWEET_URL_REGEX: () => (/* binding */ TWEET_URL_REGEX),\n/* harmony export */   TWITTER_ACCOUNT_URL_REGEX: () => (/* binding */ TWITTER_ACCOUNT_URL_REGEX),\n/* harmony export */   TWITTER_HANDLE_REGEX: () => (/* binding */ TWITTER_HANDLE_REGEX),\n/* harmony export */   handleLikeClick: () => (/* binding */ handleLikeClick),\n/* harmony export */   handleRetweetClick: () => (/* binding */ handleRetweetClick),\n/* harmony export */   tweetIdFromUrl: () => (/* binding */ tweetIdFromUrl),\n/* harmony export */   twitterHandleFromAccountUrl: () => (/* binding */ twitterHandleFromAccountUrl),\n/* harmony export */   twitterHandleFromUrl: () => (/* binding */ twitterHandleFromUrl)\n/* harmony export */ });\nconst TWEET_URL_REGEX = /^https?:\\/\\/(twitter|x)\\.com\\/(?:#!\\/)?(\\w+)\\/status(es)?\\/(\\d+)/;\n//ONLY matches account URLs\nconst TWITTER_ACCOUNT_URL_REGEX = /(twitter|x)\\.com\\/(\\w+)\\/?/;\nconst TWITTER_HANDLE_REGEX = /@([a-zA-Z0-9_]){1,15}/g;\nconst matchTweetUrl = (url)=>url.match(TWEET_URL_REGEX);\nconst matchAccountUrl = (url)=>url.match(TWITTER_ACCOUNT_URL_REGEX);\nfunction twitterHandleFromAccountUrl(url) {\n    const matchedArray = matchAccountUrl(url);\n    return matchedArray?.[2] || \"\";\n}\nfunction twitterHandleFromUrl(url) {\n    const matchedArray = matchTweetUrl(url);\n    return matchedArray?.[2] || \"\";\n}\nfunction tweetIdFromUrl(url) {\n    const matchedArray = matchTweetUrl(url);\n    return matchedArray?.[4] || \"\";\n}\nconst handleLikeClick = (tweetId)=>{\n    window.open(`https://twitter.com/intent/like?tweet_id=${tweetId}`, \"_blank\");\n};\nconst handleRetweetClick = (tweetId)=>{\n    window.open(`https://twitter.com/intent/retweet?tweet_id=${tweetId}`, \"_blank\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Twitter/twitter-helper.ts\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MediaDisplay({ block, contentState }) {\n    const entityKey = block.getEntityAt(0);\n    if (!entityKey) return null;\n    const entity = contentState.getEntity(entityKey);\n    const { src, mediaType } = entity.getData();\n    if (!src) return null;\n    if (mediaType === \"video\" || entity.getType() === \"VIDEO\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n            controls: true,\n            preload: \"metadata\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\",\n                borderRadius: \"6px\",\n                margin: \"8px 0\"\n            },\n            src: src,\n            children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: \"Rich Text Image\",\n        style: {\n            maxWidth: \"100%\",\n            height: \"auto\",\n            borderRadius: \"6px\",\n            margin: \"8px 0\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction mediaBlockRenderer(block) {\n    if (block.getType() === \"atomic\") {\n        return {\n            component: MediaDisplay,\n            editable: false\n        };\n    }\n    return null;\n}\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_4__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_4__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_4__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                className: \"jsx-7a4dfc642d828fbd\" + \" \" + ((0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\") || \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"7a4dfc642d828fbd\",\n                        children: \".RichEditor-text-align-left,.RichEditor-text-align-left .public-DraftStyleDefault-block{text-align:left!important}.RichEditor-text-align-center,.RichEditor-text-align-center .public-DraftStyleDefault-block{text-align:center!important}.RichEditor-text-align-right,.RichEditor-text-align-right .public-DraftStyleDefault-block{text-align:right!important}\"\n                    }, void 0, false, void 0, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_4__.Editor, {\n                        editorState: editorState,\n                        readOnly: readonly,\n                        onChange: ()=>{},\n                        blockRendererFn: mediaBlockRenderer,\n                        blockStyleFn: (block)=>{\n                            const blockData = block.getData();\n                            const textAlign = blockData.get(\"textAlign\") || \"left\";\n                            let className = \"\";\n                            switch(block.getType()){\n                                case \"blockquote\":\n                                    className = \"RichEditor-blockquote\";\n                                    break;\n                                default:\n                                    className = \"\";\n                            }\n                            className += ` RichEditor-text-align-${textAlign}`;\n                            return className.trim();\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1RleHRFZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ29CO0FBUXpDO0FBQ2U7QUFDTTtBQUVhO0FBRXBELFNBQVNZLGFBQWEsRUFDcEJDLEtBQUssRUFDTEMsWUFBWSxFQUliO0lBQ0MsTUFBTUMsWUFBWUYsTUFBTUcsV0FBVyxDQUFDO0lBQ3BDLElBQUksQ0FBQ0QsV0FBVyxPQUFPO0lBRXZCLE1BQU1FLFNBQVNILGFBQWFJLFNBQVMsQ0FBQ0g7SUFDdEMsTUFBTSxFQUFFSSxHQUFHLEVBQUVDLFNBQVMsRUFBRSxHQUFHSCxPQUFPSSxPQUFPO0lBRXpDLElBQUksQ0FBQ0YsS0FBSyxPQUFPO0lBRWpCLElBQUlDLGNBQWMsV0FBV0gsT0FBT0ssT0FBTyxPQUFPLFNBQVM7UUFDekQscUJBQ0UsOERBQUNDO1lBQ0NDLFFBQVE7WUFDUkMsU0FBUTtZQUNSQyxPQUFPO2dCQUNMQyxVQUFVO2dCQUNWQyxRQUFRO2dCQUNSQyxjQUFjO2dCQUNkQyxRQUFRO1lBQ1Y7WUFDQVgsS0FBS0E7c0JBQ047Ozs7OztJQUlMO0lBRUEscUJBQ0UsOERBQUNZO1FBQ0NaLEtBQUtBO1FBQ0xhLEtBQUk7UUFDSk4sT0FBTztZQUNMQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxRQUFRO1FBQ1Y7Ozs7OztBQUdOO0FBRUEsU0FBU0csbUJBQW1CcEIsS0FBbUI7SUFDN0MsSUFBSUEsTUFBTVMsT0FBTyxPQUFPLFVBQVU7UUFDaEMsT0FBTztZQUNMWSxXQUFXdEI7WUFDWHVCLFVBQVU7UUFDWjtJQUNGO0lBQ0EsT0FBTztBQUNUO0FBRWUsU0FBU0MsV0FBVyxFQUNqQ0MsV0FBVyxJQUFJLEVBQ2ZDLFlBQVksRUFDWkMsYUFBYSxLQUFLLEVBQ2xCQyxZQUFZLEdBQUcsRUFDZkMsU0FBUyxFQU9WO0lBQ0MsTUFBTUMsTUFBTWhDLDZDQUFNQSxDQUFNO0lBQ3hCLE1BQU0sQ0FBQ2lDLFlBQVlDLGNBQWMsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tDLFlBQVlDLGNBQWMsR0FBR25DLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sRUFBRW9DLEtBQUssRUFBRUMsV0FBVyxFQUFFLEdBQUd4QyxxREFBUUE7SUFDdkMsTUFBTXlDLGVBQWVGLFVBQVUsV0FBV0MsY0FBY0Q7SUFFeER0QyxnREFBU0EsQ0FBQztRQUNSLE1BQU15QyxZQUFZUixJQUFJUyxPQUFPO1FBRTdCLElBQUksQ0FBQ0QsYUFBYSxDQUFDTixlQUFlO1FBRWxDLElBQUlMLGNBQWNXLFVBQVVFLFlBQVksSUFBSVosV0FBVztZQUNyREksY0FBYztRQUNoQjtJQUNGLEdBQUc7UUFBQ0Y7UUFBS0U7S0FBYztJQUV2QixTQUFTUyxLQUFLQyxLQUFVO1FBQ3RCLE1BQU0sRUFBRUMsR0FBRyxFQUFFLEdBQUdELE1BQU14QyxZQUFZLENBQUNJLFNBQVMsQ0FBQ29DLE1BQU12QyxTQUFTLEVBQUVNLE9BQU87UUFDckUscUJBQ0UsOERBQUNtQztZQUNDQyxNQUFNRjtZQUNORyxRQUFPO1lBQ1BDLEtBQUk7WUFDSkMsZ0JBQWU7WUFDZmxDLE9BQU87Z0JBQ0xtQyxPQUFPWixpQkFBaUIsU0FBUyxZQUFZO2dCQUM3Q2EsZ0JBQWdCO1lBQ2xCO3NCQUVDUixNQUFNUyxRQUFROzs7Ozs7SUFHckI7SUFDQSxTQUFTQyxpQkFDUEMsWUFBaUIsRUFDakJDLFFBQWEsRUFDYnBELFlBQWlCO1FBRWpCbUQsYUFBYUUsZ0JBQWdCLENBQUMsQ0FBQ0M7WUFDN0IsTUFBTXJELFlBQVlxRCxVQUFVbEQsU0FBUztZQUNyQyxPQUNFSCxjQUFjLFFBQ2RELGFBQWFJLFNBQVMsQ0FBQ0gsV0FBV08sT0FBTyxPQUFPO1FBRXBELEdBQUc0QztJQUNMO0lBRUEsTUFBTUcsWUFBWSxJQUFJbEUsd0RBQWtCQSxDQUFDO1FBQ3ZDO1lBQ0VtRSxVQUFVTjtZQUNWOUIsV0FBV21CO1FBQ2I7S0FDRDtJQUNELElBQUlrQixjQUFjaEUsaURBQVdBLENBQUNpRSxXQUFXLENBQUNIO0lBRTFDLElBQUksQ0FBQy9CLGNBQWMscUJBQU87SUFFMUIsSUFBSUEsY0FBYztRQUNoQixJQUFJO1lBQ0YsTUFBTW1DLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ3JDO1lBQzlCaUMsY0FBY2hFLGlEQUFXQSxDQUFDcUUsaUJBQWlCLENBQ3pDdkUsd0RBQWNBLENBQUNvRSxhQUNmSjtRQUVKLEVBQUUsT0FBT1EsS0FBSztZQUNaTixjQUFjaEUsaURBQVdBLENBQUNxRSxpQkFBaUIsQ0FDekN4RSxrREFBWUEsQ0FBQzBFLGNBQWMsQ0FBQ3hDLGVBQzVCK0I7UUFFSjtJQUNGO0lBRUEsSUFBSSxDQUFDRSxZQUFZUSxpQkFBaUIsR0FBR0MsT0FBTyxJQUFJLHFCQUFPO0lBRXZELHFCQUNFLDhEQUFDQztRQUFJeEMsV0FBVTs7WUFDWkUsNEJBQ0M7O29CQUNHLENBQUNFLDRCQUNBLDhEQUFDb0M7d0JBQUl4QyxXQUFVOzs7Ozs7a0NBRWpCLDhEQUFDd0M7d0JBQ0N4QyxXQUFXLENBQUMsK0RBQStELENBQUM7a0NBRTVFLDRFQUFDeUM7NEJBQ0NDLFNBQVMsSUFBTXJDLGNBQWMsQ0FBQ0Q7NEJBQzlCSixXQUFVO3NDQUVUSSwyQkFDQyw4REFBQzNDLDBEQUFPQTtnQ0FBQ2tGLE1BQU07Z0NBQUlDLFFBQU87Ozs7O3FEQUUxQiw4REFBQ3BGLDREQUFTQTtnQ0FBQ21GLE1BQU07Z0NBQUlDLFFBQU87Ozs7Ozs7Ozs7Ozs7Ozs7b0JBRzNCOzs7MEJBSVgsOERBQUNKO2dCQU1DdkMsS0FBS0E7Z0JBQ0xoQixPQUNFYSxjQUFjLENBQUNNLGFBQ1g7b0JBQ0VMO29CQUNBOEMsY0FBYztnQkFDaEIsSUFDQSxDQUFDOzJEQVpJdEYscURBQUVBLENBQ1gsQ0FBQyxnREFBZ0QsQ0FBQyxFQUNsRHlDLFdBQ0FJLGFBQWEsVUFBVTs7Ozs7O2tDQThCekIsOERBQUN2Qyw0Q0FBTUE7d0JBQ0xpRSxhQUFhQTt3QkFDYmdCLFVBQVVsRDt3QkFDVm1ELFVBQVUsS0FBTzt3QkFDakJDLGlCQUFpQnhEO3dCQUNqQnlELGNBQWMsQ0FBQzdFOzRCQUNiLE1BQU04RSxZQUFZOUUsTUFBTVEsT0FBTzs0QkFDL0IsTUFBTXVFLFlBQVlELFVBQVVFLEdBQUcsQ0FBQyxnQkFBZ0I7NEJBQ2hELElBQUlwRCxZQUFZOzRCQUVoQixPQUFRNUIsTUFBTVMsT0FBTztnQ0FDbkIsS0FBSztvQ0FDSG1CLFlBQVk7b0NBQ1o7Z0NBQ0Y7b0NBQ0VBLFlBQVk7NEJBQ2hCOzRCQUVBQSxhQUFhLENBQUMsdUJBQXVCLEVBQUVtRCxVQUFVLENBQUM7NEJBQ2xELE9BQU9uRCxVQUFVcUQsSUFBSTt3QkFDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9UZXh0RWRpdG9yLnRzeD9lYjkzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5pbXBvcnQgeyBDYXJldERvd24sIENhcmV0VXAgfSBmcm9tICdAcGhvc3Bob3ItaWNvbnMvcmVhY3QnO1xyXG5pbXBvcnQge1xyXG4gIENvbXBvc2l0ZURlY29yYXRvcixcclxuICBDb250ZW50U3RhdGUsXHJcbiAgY29udmVydEZyb21SYXcsXHJcbiAgRWRpdG9yLFxyXG4gIEVkaXRvclN0YXRlLFxyXG4gIENvbnRlbnRCbG9jayxcclxufSBmcm9tICdkcmFmdC1qcyc7XHJcbmltcG9ydCAnZHJhZnQtanMvZGlzdC9EcmFmdC5jc3MnO1xyXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ25leHQtdGhlbWVzJztcclxuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5mdW5jdGlvbiBNZWRpYURpc3BsYXkoe1xyXG4gIGJsb2NrLFxyXG4gIGNvbnRlbnRTdGF0ZSxcclxufToge1xyXG4gIGJsb2NrOiBDb250ZW50QmxvY2s7XHJcbiAgY29udGVudFN0YXRlOiBDb250ZW50U3RhdGU7XHJcbn0pIHtcclxuICBjb25zdCBlbnRpdHlLZXkgPSBibG9jay5nZXRFbnRpdHlBdCgwKTtcclxuICBpZiAoIWVudGl0eUtleSkgcmV0dXJuIG51bGw7XHJcblxyXG4gIGNvbnN0IGVudGl0eSA9IGNvbnRlbnRTdGF0ZS5nZXRFbnRpdHkoZW50aXR5S2V5KTtcclxuICBjb25zdCB7IHNyYywgbWVkaWFUeXBlIH0gPSBlbnRpdHkuZ2V0RGF0YSgpO1xyXG5cclxuICBpZiAoIXNyYykgcmV0dXJuIG51bGw7XHJcblxyXG4gIGlmIChtZWRpYVR5cGUgPT09ICd2aWRlbycgfHwgZW50aXR5LmdldFR5cGUoKSA9PT0gJ1ZJREVPJykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPHZpZGVvXHJcbiAgICAgICAgY29udHJvbHNcclxuICAgICAgICBwcmVsb2FkPVwibWV0YWRhdGFcIlxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBtYXhXaWR0aDogJzEwMCUnLFxyXG4gICAgICAgICAgaGVpZ2h0OiAnYXV0bycsXHJcbiAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxyXG4gICAgICAgICAgbWFyZ2luOiAnOHB4IDAnLFxyXG4gICAgICAgIH19XHJcbiAgICAgICAgc3JjPXtzcmN9XHJcbiAgICAgID5cclxuICAgICAgICBZb3VyIGJyb3dzZXIgZG9lcyBub3Qgc3VwcG9ydCB0aGUgdmlkZW8gdGFnLlxyXG4gICAgICA8L3ZpZGVvPlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8aW1nXHJcbiAgICAgIHNyYz17c3JjfVxyXG4gICAgICBhbHQ9XCJSaWNoIFRleHQgSW1hZ2VcIlxyXG4gICAgICBzdHlsZT17e1xyXG4gICAgICAgIG1heFdpZHRoOiAnMTAwJScsXHJcbiAgICAgICAgaGVpZ2h0OiAnYXV0bycsXHJcbiAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcclxuICAgICAgICBtYXJnaW46ICc4cHggMCcsXHJcbiAgICAgIH19XHJcbiAgICAvPlxyXG4gICk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIG1lZGlhQmxvY2tSZW5kZXJlcihibG9jazogQ29udGVudEJsb2NrKSB7XHJcbiAgaWYgKGJsb2NrLmdldFR5cGUoKSA9PT0gJ2F0b21pYycpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGNvbXBvbmVudDogTWVkaWFEaXNwbGF5LFxyXG4gICAgICBlZGl0YWJsZTogZmFsc2UsXHJcbiAgICB9O1xyXG4gIH1cclxuICByZXR1cm4gbnVsbDtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGV4dEVkaXRvcih7XHJcbiAgcmVhZG9ubHkgPSB0cnVlLFxyXG4gIGRlZmF1bHRWYWx1ZSxcclxuICBleHBhbmRhYmxlID0gZmFsc2UsXHJcbiAgbWF4SGVpZ2h0ID0gMTAwLFxyXG4gIGNsYXNzTmFtZSxcclxufToge1xyXG4gIGRlZmF1bHRWYWx1ZTogc3RyaW5nO1xyXG4gIHJlYWRvbmx5PzogYm9vbGVhbjtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBtYXhIZWlnaHQ/OiBudW1iZXI7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59KSB7XHJcbiAgY29uc3QgcmVmID0gdXNlUmVmPGFueT4obnVsbCk7XHJcbiAgY29uc3QgW2lzT3ZlcmZsb3csIHNldElzT3ZlcmZsb3ddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0V4cGFuZGVkLCBzZXRJc0V4cGFuZGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB7IHRoZW1lLCBzeXN0ZW1UaGVtZSB9ID0gdXNlVGhlbWUoKTtcclxuICBjb25zdCBjdXJyZW50VGhlbWUgPSB0aGVtZSA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1UaGVtZSA6IHRoZW1lO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY29udGFpbmVyID0gcmVmLmN1cnJlbnQ7XHJcblxyXG4gICAgaWYgKCFjb250YWluZXIgfHwgIXNldElzT3ZlcmZsb3cpIHJldHVybjtcclxuXHJcbiAgICBpZiAoZXhwYW5kYWJsZSAmJiBjb250YWluZXIub2Zmc2V0SGVpZ2h0ID49IG1heEhlaWdodCkge1xyXG4gICAgICBzZXRJc092ZXJmbG93KHRydWUpO1xyXG4gICAgfVxyXG4gIH0sIFtyZWYsIHNldElzT3ZlcmZsb3ddKTtcclxuXHJcbiAgZnVuY3Rpb24gTGluayhwcm9wczogYW55KSB7XHJcbiAgICBjb25zdCB7IHVybCB9ID0gcHJvcHMuY29udGVudFN0YXRlLmdldEVudGl0eShwcm9wcy5lbnRpdHlLZXkpLmdldERhdGEoKTtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxhXHJcbiAgICAgICAgaHJlZj17dXJsfVxyXG4gICAgICAgIHRhcmdldD1cIl9ibGFua1wiXHJcbiAgICAgICAgcmVsPVwibm9yZWZlcnJlciBub2ZvbGxvd1wiXHJcbiAgICAgICAgcmVmZXJyZXJQb2xpY3k9XCJuby1yZWZlcnJlclwiXHJcbiAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgIGNvbG9yOiBjdXJyZW50VGhlbWUgPT09ICdkYXJrJyA/ICcjOTNjZWZlJyA6ICcjM2I1OTk4JyxcclxuICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAndW5kZXJsaW5lJyxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge3Byb3BzLmNoaWxkcmVufVxyXG4gICAgICA8L2E+XHJcbiAgICApO1xyXG4gIH1cclxuICBmdW5jdGlvbiBmaW5kTGlua0VudGl0aWVzKFxyXG4gICAgY29udGVudEJsb2NrOiBhbnksXHJcbiAgICBjYWxsYmFjazogYW55LFxyXG4gICAgY29udGVudFN0YXRlOiBhbnksXHJcbiAgKSB7XHJcbiAgICBjb250ZW50QmxvY2suZmluZEVudGl0eVJhbmdlcygoY2hhcmFjdGVyOiBhbnkpID0+IHtcclxuICAgICAgY29uc3QgZW50aXR5S2V5ID0gY2hhcmFjdGVyLmdldEVudGl0eSgpO1xyXG4gICAgICByZXR1cm4gKFxyXG4gICAgICAgIGVudGl0eUtleSAhPT0gbnVsbCAmJlxyXG4gICAgICAgIGNvbnRlbnRTdGF0ZS5nZXRFbnRpdHkoZW50aXR5S2V5KS5nZXRUeXBlKCkgPT09ICdMSU5LJ1xyXG4gICAgICApO1xyXG4gICAgfSwgY2FsbGJhY2spO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgZGVjb3JhdG9yID0gbmV3IENvbXBvc2l0ZURlY29yYXRvcihbXHJcbiAgICB7XHJcbiAgICAgIHN0cmF0ZWd5OiBmaW5kTGlua0VudGl0aWVzLFxyXG4gICAgICBjb21wb25lbnQ6IExpbmssXHJcbiAgICB9LFxyXG4gIF0pO1xyXG4gIGxldCBlZGl0b3JTdGF0ZSA9IEVkaXRvclN0YXRlLmNyZWF0ZUVtcHR5KGRlY29yYXRvcik7XHJcblxyXG4gIGlmICghZGVmYXVsdFZhbHVlKSByZXR1cm4gPD48Lz47XHJcblxyXG4gIGlmIChkZWZhdWx0VmFsdWUpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHBhcnNlZEpzb24gPSBKU09OLnBhcnNlKGRlZmF1bHRWYWx1ZSk7XHJcbiAgICAgIGVkaXRvclN0YXRlID0gRWRpdG9yU3RhdGUuY3JlYXRlV2l0aENvbnRlbnQoXHJcbiAgICAgICAgY29udmVydEZyb21SYXcocGFyc2VkSnNvbiksXHJcbiAgICAgICAgZGVjb3JhdG9yLFxyXG4gICAgICApO1xyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGVkaXRvclN0YXRlID0gRWRpdG9yU3RhdGUuY3JlYXRlV2l0aENvbnRlbnQoXHJcbiAgICAgICAgQ29udGVudFN0YXRlLmNyZWF0ZUZyb21UZXh0KGRlZmF1bHRWYWx1ZSksXHJcbiAgICAgICAgZGVjb3JhdG9yLFxyXG4gICAgICApO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgaWYgKCFlZGl0b3JTdGF0ZS5nZXRDdXJyZW50Q29udGVudCgpLmhhc1RleHQoKSkgcmV0dXJuIDw+PC8+O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICB7aXNPdmVyZmxvdyAmJiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIHshaXNFeHBhbmRlZCAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgei0xMCBoLVs0MHB4XSB3LWZ1bGwgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJhY2tncm91bmQvNjAgdG8tYmFja2dyb3VuZC8wXCI+PC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSB6LTIwIGZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBqdXN0aWZ5LWNlbnRlciAtYm90dG9tLTNgfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFeHBhbmRlZCghaXNFeHBhbmRlZCl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHotMTAgcm91bmRlZC1mdWxsIGJvcmRlciBiZy1iYWNrZ3JvdW5kLzkwIHRleHQtY3MgdGV4dC1zbSBmb250LWV4dHJhYm9sZFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7aXNFeHBhbmRlZCA/IChcclxuICAgICAgICAgICAgICAgIDxDYXJldFVwIHNpemU9ezE2fSB3ZWlnaHQ9XCJib2xkXCIgLz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPENhcmV0RG93biBzaXplPXsxNn0gd2VpZ2h0PVwiYm9sZFwiIC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj57JyAnfVxyXG4gICAgICAgIDwvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICBgdGV4dC1iYXNlIHRleHQtY2ggbGVhZGluZy1ub3JtYWwgb3ZlcmZsb3ctaGlkZGVuYCxcclxuICAgICAgICAgIGNsYXNzTmFtZSxcclxuICAgICAgICAgIGlzRXhwYW5kZWQgPyAnbWItMTAnIDogJycsXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICBzdHlsZT17XHJcbiAgICAgICAgICBleHBhbmRhYmxlICYmICFpc0V4cGFuZGVkXHJcbiAgICAgICAgICAgID8ge1xyXG4gICAgICAgICAgICAgICAgbWF4SGVpZ2h0LFxyXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAwLFxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgOiB7fVxyXG4gICAgICAgIH1cclxuICAgICAgPlxyXG4gICAgICAgIDxzdHlsZSBqc3g+e2BcclxuICAgICAgICAgIDpnbG9iYWwoLlJpY2hFZGl0b3ItdGV4dC1hbGlnbi1sZWZ0KSxcclxuICAgICAgICAgIDpnbG9iYWwoLlJpY2hFZGl0b3ItdGV4dC1hbGlnbi1sZWZ0IC5wdWJsaWMtRHJhZnRTdHlsZURlZmF1bHQtYmxvY2spIHtcclxuICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgOmdsb2JhbCguUmljaEVkaXRvci10ZXh0LWFsaWduLWNlbnRlciksXHJcbiAgICAgICAgICA6Z2xvYmFsKFxyXG4gICAgICAgICAgICAgIC5SaWNoRWRpdG9yLXRleHQtYWxpZ24tY2VudGVyIC5wdWJsaWMtRHJhZnRTdHlsZURlZmF1bHQtYmxvY2tcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgOmdsb2JhbCguUmljaEVkaXRvci10ZXh0LWFsaWduLXJpZ2h0KSxcclxuICAgICAgICAgIDpnbG9iYWwoXHJcbiAgICAgICAgICAgICAgLlJpY2hFZGl0b3ItdGV4dC1hbGlnbi1yaWdodCAucHVibGljLURyYWZ0U3R5bGVEZWZhdWx0LWJsb2NrXHJcbiAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIGB9PC9zdHlsZT5cclxuICAgICAgICA8RWRpdG9yXHJcbiAgICAgICAgICBlZGl0b3JTdGF0ZT17ZWRpdG9yU3RhdGV9XHJcbiAgICAgICAgICByZWFkT25seT17cmVhZG9ubHl9XHJcbiAgICAgICAgICBvbkNoYW5nZT17KCkgPT4ge319XHJcbiAgICAgICAgICBibG9ja1JlbmRlcmVyRm49e21lZGlhQmxvY2tSZW5kZXJlcn1cclxuICAgICAgICAgIGJsb2NrU3R5bGVGbj17KGJsb2NrKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGJsb2NrRGF0YSA9IGJsb2NrLmdldERhdGEoKTtcclxuICAgICAgICAgICAgY29uc3QgdGV4dEFsaWduID0gYmxvY2tEYXRhLmdldCgndGV4dEFsaWduJykgfHwgJ2xlZnQnO1xyXG4gICAgICAgICAgICBsZXQgY2xhc3NOYW1lID0gJyc7XHJcblxyXG4gICAgICAgICAgICBzd2l0Y2ggKGJsb2NrLmdldFR5cGUoKSkge1xyXG4gICAgICAgICAgICAgIGNhc2UgJ2Jsb2NrcXVvdGUnOlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lID0gJ1JpY2hFZGl0b3ItYmxvY2txdW90ZSc7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lID0gJyc7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZSArPSBgIFJpY2hFZGl0b3ItdGV4dC1hbGlnbi0ke3RleHRBbGlnbn1gO1xyXG4gICAgICAgICAgICByZXR1cm4gY2xhc3NOYW1lLnRyaW0oKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJjbiIsIkNhcmV0RG93biIsIkNhcmV0VXAiLCJDb21wb3NpdGVEZWNvcmF0b3IiLCJDb250ZW50U3RhdGUiLCJjb252ZXJ0RnJvbVJhdyIsIkVkaXRvciIsIkVkaXRvclN0YXRlIiwidXNlVGhlbWUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIk1lZGlhRGlzcGxheSIsImJsb2NrIiwiY29udGVudFN0YXRlIiwiZW50aXR5S2V5IiwiZ2V0RW50aXR5QXQiLCJlbnRpdHkiLCJnZXRFbnRpdHkiLCJzcmMiLCJtZWRpYVR5cGUiLCJnZXREYXRhIiwiZ2V0VHlwZSIsInZpZGVvIiwiY29udHJvbHMiLCJwcmVsb2FkIiwic3R5bGUiLCJtYXhXaWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsIm1hcmdpbiIsImltZyIsImFsdCIsIm1lZGlhQmxvY2tSZW5kZXJlciIsImNvbXBvbmVudCIsImVkaXRhYmxlIiwiVGV4dEVkaXRvciIsInJlYWRvbmx5IiwiZGVmYXVsdFZhbHVlIiwiZXhwYW5kYWJsZSIsIm1heEhlaWdodCIsImNsYXNzTmFtZSIsInJlZiIsImlzT3ZlcmZsb3ciLCJzZXRJc092ZXJmbG93IiwiaXNFeHBhbmRlZCIsInNldElzRXhwYW5kZWQiLCJ0aGVtZSIsInN5c3RlbVRoZW1lIiwiY3VycmVudFRoZW1lIiwiY29udGFpbmVyIiwiY3VycmVudCIsIm9mZnNldEhlaWdodCIsIkxpbmsiLCJwcm9wcyIsInVybCIsImEiLCJocmVmIiwidGFyZ2V0IiwicmVsIiwicmVmZXJyZXJQb2xpY3kiLCJjb2xvciIsInRleHREZWNvcmF0aW9uIiwiY2hpbGRyZW4iLCJmaW5kTGlua0VudGl0aWVzIiwiY29udGVudEJsb2NrIiwiY2FsbGJhY2siLCJmaW5kRW50aXR5UmFuZ2VzIiwiY2hhcmFjdGVyIiwiZGVjb3JhdG9yIiwic3RyYXRlZ3kiLCJlZGl0b3JTdGF0ZSIsImNyZWF0ZUVtcHR5IiwicGFyc2VkSnNvbiIsIkpTT04iLCJwYXJzZSIsImNyZWF0ZVdpdGhDb250ZW50IiwiZXJyIiwiY3JlYXRlRnJvbVRleHQiLCJnZXRDdXJyZW50Q29udGVudCIsImhhc1RleHQiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwic2l6ZSIsIndlaWdodCIsIm1hcmdpbkJvdHRvbSIsInJlYWRPbmx5Iiwib25DaGFuZ2UiLCJibG9ja1JlbmRlcmVyRm4iLCJibG9ja1N0eWxlRm4iLCJibG9ja0RhdGEiLCJ0ZXh0QWxpZ24iLCJnZXQiLCJ0cmltIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ }),

/***/ "./components/TimeLine/TimeLineItem.tsx":
/*!**********************************************!*\
  !*** ./components/TimeLine/TimeLineItem.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index */ \"./components/TimeLine/index.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst TimeLineItem = ({ icon, title, children, className, extraChildMargin = true, status = _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.NotStarted, ring = false, pulse = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(`flex gap-2 text-base text-ch font-medium leading-tight`, extraChildMargin ? \"mb-4\" : \"mb-1\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(`flex flex-shrink-0 text-sm font-medium items-center justify-center w-6 h-6 rounded-full`, status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.Active ? `bg-primary text-primary-foreground ${ring ? \"ring-8 ring-gray-200\" : \"\"} ${pulse && \"pulse\"}` : \"\", status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.NotStarted ? \"bg-foreground/10 text-cl\" : \"\"),\n                        children: status === _index__WEBPACK_IMPORTED_MODULE_2__.TimeLineStatus.Completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_1__.Check, {\n                            size: 20,\n                            className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                            weight: \"bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, undefined) : icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\TimeLineItem.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimeLineItem);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TimeLine/TimeLineItem.tsx\n");

/***/ }),

/***/ "./components/TimeLine/index.tsx":
/*!***************************************!*\
  !*** ./components/TimeLine/index.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeLineStatus: () => (/* binding */ TimeLineStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nvar TimeLineStatus;\n(function(TimeLineStatus) {\n    TimeLineStatus[TimeLineStatus[\"Active\"] = 0] = \"Active\";\n    TimeLineStatus[TimeLineStatus[\"NotStarted\"] = 1] = \"NotStarted\";\n    TimeLineStatus[TimeLineStatus[\"Completed\"] = 2] = \"Completed\";\n})(TimeLineStatus || (TimeLineStatus = {}));\nconst TimeLine = ({ data, currentStep, render })=>{\n    const getItemStatus = (step)=>{\n        if (currentStep === step) return 0;\n        if (currentStep < step) return 1;\n        return 2;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n        className: \"relative space-y-10\",\n        children: data.map((item, step)=>render(item, step, getItemStatus(step)))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TimeLine\\\\index.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TimeLine);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1RpbWVMaW5lL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7VUFBWUE7Ozs7R0FBQUEsbUJBQUFBO0FBTVosTUFBTUMsV0FBVyxDQUFLLEVBQ3BCQyxJQUFJLEVBQ0pDLFdBQVcsRUFDWEMsTUFBTSxFQUtQO0lBQ0MsTUFBTUMsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlILGdCQUFnQkcsTUFBTTtRQUMxQixJQUFJSCxjQUFjRyxNQUFNO1FBQ3hCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBR0MsV0FBVTtrQkFDWE4sS0FBS08sR0FBRyxDQUFDLENBQUNDLE1BQU1KLE9BQVNGLE9BQU9NLE1BQU1KLE1BQU1ELGNBQWNDOzs7Ozs7QUFHakU7QUFFQSxpRUFBZUwsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvVGltZUxpbmUvaW5kZXgudHN4PzdmMzgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGVudW0gVGltZUxpbmVTdGF0dXMge1xyXG4gIEFjdGl2ZSxcclxuICBOb3RTdGFydGVkLFxyXG4gIENvbXBsZXRlZCxcclxufVxyXG5cclxuY29uc3QgVGltZUxpbmUgPSA8VCw+KHtcclxuICBkYXRhLFxyXG4gIGN1cnJlbnRTdGVwLFxyXG4gIHJlbmRlcixcclxufToge1xyXG4gIGRhdGE6IEFycmF5PFQ+O1xyXG4gIGN1cnJlbnRTdGVwOiBudW1iZXI7XHJcbiAgcmVuZGVyOiAoaXRlbTogVCwgc3RlcDogbnVtYmVyLCBzdGF0dXM6IFRpbWVMaW5lU3RhdHVzKSA9PiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICBjb25zdCBnZXRJdGVtU3RhdHVzID0gKHN0ZXA6IG51bWJlcikgPT4ge1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSBzdGVwKSByZXR1cm4gVGltZUxpbmVTdGF0dXMuQWN0aXZlO1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwIDwgc3RlcCkgcmV0dXJuIFRpbWVMaW5lU3RhdHVzLk5vdFN0YXJ0ZWQ7XHJcbiAgICByZXR1cm4gVGltZUxpbmVTdGF0dXMuQ29tcGxldGVkO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8b2wgY2xhc3NOYW1lPVwicmVsYXRpdmUgc3BhY2UteS0xMFwiPlxyXG4gICAgICB7ZGF0YS5tYXAoKGl0ZW0sIHN0ZXApID0+IHJlbmRlcihpdGVtLCBzdGVwLCBnZXRJdGVtU3RhdHVzKHN0ZXApKSl9XHJcbiAgICA8L29sPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBUaW1lTGluZTtcclxuIl0sIm5hbWVzIjpbIlRpbWVMaW5lU3RhdHVzIiwiVGltZUxpbmUiLCJkYXRhIiwiY3VycmVudFN0ZXAiLCJyZW5kZXIiLCJnZXRJdGVtU3RhdHVzIiwic3RlcCIsIm9sIiwiY2xhc3NOYW1lIiwibWFwIiwiaXRlbSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/TimeLine/index.tsx\n");

/***/ }),

/***/ "./components/TweetEmbed.tsx":
/*!***********************************!*\
  !*** ./components/TweetEmbed.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmbedTweet),\n/* harmony export */   useScript: () => (/* binding */ useScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction useScript(src) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(src ? \"loading\" : \"idle\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!src) {\n            setStatus(\"idle\");\n            return;\n        }\n        let script = document.querySelector(`script[src=\"${src}\"]`);\n        if (!script) {\n            script = document.createElement(\"script\");\n            script.src = src;\n            script.async = true;\n            script.setAttribute(\"data-status\", \"loading\");\n            document.body.appendChild(script);\n            const setAttributeFromEvent = (event)=>{\n                script?.setAttribute(\"data-status\", event.type === \"load\" ? \"ready\" : \"error\");\n            };\n            script.addEventListener(\"load\", setAttributeFromEvent);\n            script.addEventListener(\"error\", setAttributeFromEvent);\n        } else {\n            setStatus(script.getAttribute(\"data-status\"));\n        }\n        const setStateFromEvent = (event)=>{\n            setStatus(event.type === \"load\" ? \"ready\" : \"error\");\n        };\n        script.addEventListener(\"load\", setStateFromEvent);\n        script.addEventListener(\"error\", setStateFromEvent);\n        return ()=>{\n            if (script) {\n                script.removeEventListener(\"load\", setStateFromEvent);\n                script.removeEventListener(\"error\", setStateFromEvent);\n            }\n        };\n    }, [\n        src\n    ]);\n    return status;\n}\nconst TWITTER_WIDGET_URL = \"https://platform.twitter.com/widgets.js\";\nfunction EmbedTweet({ tweetId, libUrl = TWITTER_WIDGET_URL, options, placeholder = \"Loading ...\", onTweetLoadSuccess, onTweetLoadError, ...rest }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const scriptStatus = useScript(TWITTER_WIDGET_URL);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const el = ref.current;\n        let isComponentMounted = true;\n        if (scriptStatus !== \"ready\") return;\n        window[\"twttr\"].ready().then(({ widgets })=>{\n            if (isComponentMounted) {\n                widgets.createTweet(tweetId, el, {\n                    ...options,\n                    cards: \"hidden\",\n                    theme: currentTheme\n                }).then((twitterWidgetElement)=>{\n                    setLoading(false);\n                    onTweetLoadSuccess?.(twitterWidgetElement);\n                }).catch(onTweetLoadError);\n            }\n        });\n        // cleaning up\n        return ()=>{\n            isComponentMounted = false;\n        };\n    }, [\n        tweetId,\n        scriptStatus,\n        ref,\n        onTweetLoadSuccess,\n        onTweetLoadError,\n        options\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n        children: [\n            loading && placeholder,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"twitter-embed\",\n                ref: ref,\n                ...rest\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TweetEmbed.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TweetEmbed.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1R3ZWV0RW1iZWQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF1QztBQUNtQztBQUluRSxTQUFTSyxVQUFVQyxHQUFXO0lBQ25DLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHSiwrQ0FBUUEsQ0FBU0UsTUFBTSxZQUFZO0lBQy9ESixnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0ksS0FBSztZQUNSRSxVQUFVO1lBQ1Y7UUFDRjtRQUVBLElBQUlDLFNBQW1DQyxTQUFTQyxhQUFhLENBQzNELENBQUMsWUFBWSxFQUFFTCxJQUFJLEVBQUUsQ0FBQztRQUd4QixJQUFJLENBQUNHLFFBQVE7WUFDWEEsU0FBU0MsU0FBU0UsYUFBYSxDQUFDO1lBQ2hDSCxPQUFPSCxHQUFHLEdBQUdBO1lBQ2JHLE9BQU9JLEtBQUssR0FBRztZQUNmSixPQUFPSyxZQUFZLENBQUMsZUFBZTtZQUNuQ0osU0FBU0ssSUFBSSxDQUFDQyxXQUFXLENBQUNQO1lBRTFCLE1BQU1RLHdCQUF3QixDQUFDQztnQkFDN0JULFFBQVFLLGFBQ04sZUFDQUksTUFBTUMsSUFBSSxLQUFLLFNBQVMsVUFBVTtZQUV0QztZQUNBVixPQUFPVyxnQkFBZ0IsQ0FBQyxRQUFRSDtZQUNoQ1IsT0FBT1csZ0JBQWdCLENBQUMsU0FBU0g7UUFDbkMsT0FBTztZQUNMVCxVQUFVQyxPQUFPWSxZQUFZLENBQUM7UUFDaEM7UUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ0o7WUFDekJWLFVBQVVVLE1BQU1DLElBQUksS0FBSyxTQUFTLFVBQVU7UUFDOUM7UUFFQVYsT0FBT1csZ0JBQWdCLENBQUMsUUFBUUU7UUFDaENiLE9BQU9XLGdCQUFnQixDQUFDLFNBQVNFO1FBRWpDLE9BQU87WUFDTCxJQUFJYixRQUFRO2dCQUNWQSxPQUFPYyxtQkFBbUIsQ0FBQyxRQUFRRDtnQkFDbkNiLE9BQU9jLG1CQUFtQixDQUFDLFNBQVNEO1lBQ3RDO1FBQ0Y7SUFDRixHQUFHO1FBQUNoQjtLQUFJO0lBQ1IsT0FBT0M7QUFDVDtBQXlCQSxNQUFNaUIscUJBQXFCO0FBVVosU0FBU0MsV0FBVyxFQUNqQ0MsT0FBTyxFQUNQQyxTQUFTSCxrQkFBa0IsRUFDM0JJLE9BQU8sRUFDUEMsY0FBYyxhQUFhLEVBQzNCQyxrQkFBa0IsRUFDbEJDLGdCQUFnQixFQUNoQixHQUFHQyxNQUNhO0lBQ2hCLE1BQU1DLE1BQU05Qiw2Q0FBTUEsQ0FBaUI7SUFDbkMsTUFBTStCLGVBQWU3QixVQUFVbUI7SUFDL0IsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLEVBQUVpQyxLQUFLLEVBQUVDLFdBQVcsRUFBRSxHQUFHdEMscURBQVFBO0lBRXZDLE1BQU11QyxlQUFlRixVQUFVLFdBQVdDLGNBQWNEO0lBRXhEbkMsZ0RBQVNBLENBQUM7UUFDUixNQUFNc0MsS0FBS1AsSUFBSVEsT0FBTztRQUV0QixJQUFJQyxxQkFBcUI7UUFFekIsSUFBSVIsaUJBQWlCLFNBQVM7UUFDN0JTLE1BQWMsQ0FBQyxRQUFRLENBQ3JCQyxLQUFLLEdBQ0xDLElBQUksQ0FBQyxDQUFDLEVBQUVDLE9BQU8sRUFBOEI7WUFDNUMsSUFBSUosb0JBQW9CO2dCQUN0QkksUUFDR0MsV0FBVyxDQUFDckIsU0FBU2MsSUFBSTtvQkFDeEIsR0FBR1osT0FBTztvQkFDVm9CLE9BQU87b0JBQ1BYLE9BQU9FO2dCQUNULEdBQ0NNLElBQUksQ0FBQyxDQUFDSTtvQkFDTGIsV0FBVztvQkFDWE4scUJBQXFCbUI7Z0JBQ3ZCLEdBQ0NDLEtBQUssQ0FBQ25CO1lBQ1g7UUFDRjtRQUVGLGNBQWM7UUFDZCxPQUFPO1lBQ0xXLHFCQUFxQjtRQUN2QjtJQUNGLEdBQUc7UUFDRGhCO1FBQ0FRO1FBQ0FEO1FBQ0FIO1FBQ0FDO1FBQ0FIO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQzNCLHVEQUFjOztZQUNaa0MsV0FBV047MEJBQ1osOERBQUN1QjtnQkFBSUMsV0FBVTtnQkFBZ0JwQixLQUFLQTtnQkFBTSxHQUFHRCxJQUFJOzs7Ozs7Ozs7Ozs7QUFHdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1R3ZWV0RW1iZWQudHN4PzY4M2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tICduZXh0LXRoZW1lcyc7XHJcbmltcG9ydCBSZWFjdCwgeyBDU1NQcm9wZXJ0aWVzLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG50eXBlIFN0YXR1cyA9ICdpZGxlJyB8ICdsb2FkaW5nJyB8ICdyZWFkeScgfCAnZXJyb3InO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZVNjcmlwdChzcmM6IHN0cmluZykge1xyXG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZTxTdGF0dXM+KHNyYyA/ICdsb2FkaW5nJyA6ICdpZGxlJyk7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghc3JjKSB7XHJcbiAgICAgIHNldFN0YXR1cygnaWRsZScpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgbGV0IHNjcmlwdDogSFRNTFNjcmlwdEVsZW1lbnQgfCBudWxsID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihcclxuICAgICAgYHNjcmlwdFtzcmM9XCIke3NyY31cIl1gLFxyXG4gICAgKSBhcyBIVE1MU2NyaXB0RWxlbWVudDtcclxuXHJcbiAgICBpZiAoIXNjcmlwdCkge1xyXG4gICAgICBzY3JpcHQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzY3JpcHQnKSBhcyBIVE1MU2NyaXB0RWxlbWVudDtcclxuICAgICAgc2NyaXB0LnNyYyA9IHNyYztcclxuICAgICAgc2NyaXB0LmFzeW5jID0gdHJ1ZTtcclxuICAgICAgc2NyaXB0LnNldEF0dHJpYnV0ZSgnZGF0YS1zdGF0dXMnLCAnbG9hZGluZycpO1xyXG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHNjcmlwdCk7XHJcblxyXG4gICAgICBjb25zdCBzZXRBdHRyaWJ1dGVGcm9tRXZlbnQgPSAoZXZlbnQ6IEV2ZW50KSA9PiB7XHJcbiAgICAgICAgc2NyaXB0Py5zZXRBdHRyaWJ1dGUoXHJcbiAgICAgICAgICAnZGF0YS1zdGF0dXMnLFxyXG4gICAgICAgICAgZXZlbnQudHlwZSA9PT0gJ2xvYWQnID8gJ3JlYWR5JyA6ICdlcnJvcicsXHJcbiAgICAgICAgKTtcclxuICAgICAgfTtcclxuICAgICAgc2NyaXB0LmFkZEV2ZW50TGlzdGVuZXIoJ2xvYWQnLCBzZXRBdHRyaWJ1dGVGcm9tRXZlbnQpO1xyXG4gICAgICBzY3JpcHQuYWRkRXZlbnRMaXN0ZW5lcignZXJyb3InLCBzZXRBdHRyaWJ1dGVGcm9tRXZlbnQpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0U3RhdHVzKHNjcmlwdC5nZXRBdHRyaWJ1dGUoJ2RhdGEtc3RhdHVzJykgYXMgU3RhdHVzKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBzZXRTdGF0ZUZyb21FdmVudCA9IChldmVudDogRXZlbnQpID0+IHtcclxuICAgICAgc2V0U3RhdHVzKGV2ZW50LnR5cGUgPT09ICdsb2FkJyA/ICdyZWFkeScgOiAnZXJyb3InKTtcclxuICAgIH07XHJcblxyXG4gICAgc2NyaXB0LmFkZEV2ZW50TGlzdGVuZXIoJ2xvYWQnLCBzZXRTdGF0ZUZyb21FdmVudCk7XHJcbiAgICBzY3JpcHQuYWRkRXZlbnRMaXN0ZW5lcignZXJyb3InLCBzZXRTdGF0ZUZyb21FdmVudCk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKHNjcmlwdCkge1xyXG4gICAgICAgIHNjcmlwdC5yZW1vdmVFdmVudExpc3RlbmVyKCdsb2FkJywgc2V0U3RhdGVGcm9tRXZlbnQpO1xyXG4gICAgICAgIHNjcmlwdC5yZW1vdmVFdmVudExpc3RlbmVyKCdlcnJvcicsIHNldFN0YXRlRnJvbUV2ZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbc3JjXSk7XHJcbiAgcmV0dXJuIHN0YXR1cztcclxufVxyXG5cclxuaW50ZXJmYWNlIFR3ZWV0T3B0aW9ucyB7XHJcbiAgY2FyZHM/OiAnaGlkZGVuJztcclxuICBjb252ZXJzYXRpb24/OiAnbm9uZSc7XHJcbiAgdGhlbWU/OiAnbGlnaHQnIHwgJ2RhcmsnO1xyXG4gIHdpZHRoPzogbnVtYmVyO1xyXG4gIGFsaWduPzogJ2xlZnQnIHwgJ3JpZ2h0JyB8ICdjZW50ZXInO1xyXG4gIGxhbmc/OiBzdHJpbmc7XHJcbiAgZG50PzogYm9vbGVhbjtcclxuICBoaWRlX21lZGlhPzogYm9vbGVhbjtcclxuICBvbWl0X3NjcmlwdD86IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEVtYmVkVHdlZXRQcm9wcyB7XHJcbiAgdHdlZXRJZDogc3RyaW5nO1xyXG4gIGxpYlVybD86IHN0cmluZztcclxuICBvcHRpb25zPzogVHdlZXRPcHRpb25zO1xyXG4gIHBsYWNlaG9sZGVyPzogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIG9uVHdlZXRMb2FkU3VjY2Vzcz86ICh0d2l0dGVyV2lkZ2V0RWxlbWVudDogSFRNTEVsZW1lbnQpID0+IGFueTtcclxuICBvblR3ZWV0TG9hZEVycm9yPzogKGVycjogRXJyb3IpID0+IGFueTtcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbiAgc3R5bGU/OiBDU1NQcm9wZXJ0aWVzO1xyXG59XHJcblxyXG5jb25zdCBUV0lUVEVSX1dJREdFVF9VUkwgPSAnaHR0cHM6Ly9wbGF0Zm9ybS50d2l0dGVyLmNvbS93aWRnZXRzLmpzJztcclxuXHJcbmludGVyZmFjZSBUd2l0dGVyV2lkZ2V0IHtcclxuICBjcmVhdGVUd2VldChcclxuICAgIHR3ZWV0SWQ6IHN0cmluZyxcclxuICAgIHRhcmdldEVsOiBIVE1MRWxlbWVudCB8IG51bGwsXHJcbiAgICBvcHRpb25zPzogVHdlZXRPcHRpb25zLFxyXG4gICk6IFByb21pc2U8YW55PjtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRW1iZWRUd2VldCh7XHJcbiAgdHdlZXRJZCxcclxuICBsaWJVcmwgPSBUV0lUVEVSX1dJREdFVF9VUkwsXHJcbiAgb3B0aW9ucyxcclxuICBwbGFjZWhvbGRlciA9ICdMb2FkaW5nIC4uLicsXHJcbiAgb25Ud2VldExvYWRTdWNjZXNzLFxyXG4gIG9uVHdlZXRMb2FkRXJyb3IsXHJcbiAgLi4ucmVzdFxyXG59OiBFbWJlZFR3ZWV0UHJvcHMpIHtcclxuICBjb25zdCByZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IHNjcmlwdFN0YXR1cyA9IHVzZVNjcmlwdChUV0lUVEVSX1dJREdFVF9VUkwpO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IHsgdGhlbWUsIHN5c3RlbVRoZW1lIH0gPSB1c2VUaGVtZSgpO1xyXG5cclxuICBjb25zdCBjdXJyZW50VGhlbWUgPSB0aGVtZSA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1UaGVtZSA6IHRoZW1lO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZWwgPSByZWYuY3VycmVudDtcclxuXHJcbiAgICBsZXQgaXNDb21wb25lbnRNb3VudGVkID0gdHJ1ZTtcclxuXHJcbiAgICBpZiAoc2NyaXB0U3RhdHVzICE9PSAncmVhZHknKSByZXR1cm47XHJcbiAgICAod2luZG93IGFzIGFueSlbJ3R3dHRyJ11cclxuICAgICAgLnJlYWR5KClcclxuICAgICAgLnRoZW4oKHsgd2lkZ2V0cyB9OiB7IHdpZGdldHM6IFR3aXR0ZXJXaWRnZXQgfSkgPT4ge1xyXG4gICAgICAgIGlmIChpc0NvbXBvbmVudE1vdW50ZWQpIHtcclxuICAgICAgICAgIHdpZGdldHNcclxuICAgICAgICAgICAgLmNyZWF0ZVR3ZWV0KHR3ZWV0SWQsIGVsLCB7XHJcbiAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcclxuICAgICAgICAgICAgICBjYXJkczogJ2hpZGRlbicsXHJcbiAgICAgICAgICAgICAgdGhlbWU6IGN1cnJlbnRUaGVtZSBhcyBhbnksXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIC50aGVuKCh0d2l0dGVyV2lkZ2V0RWxlbWVudDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICAgICAgb25Ud2VldExvYWRTdWNjZXNzPy4odHdpdHRlcldpZGdldEVsZW1lbnQpO1xyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAuY2F0Y2gob25Ud2VldExvYWRFcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAvLyBjbGVhbmluZyB1cFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaXNDb21wb25lbnRNb3VudGVkID0gZmFsc2U7XHJcbiAgICB9O1xyXG4gIH0sIFtcclxuICAgIHR3ZWV0SWQsXHJcbiAgICBzY3JpcHRTdGF0dXMsXHJcbiAgICByZWYsXHJcbiAgICBvblR3ZWV0TG9hZFN1Y2Nlc3MsXHJcbiAgICBvblR3ZWV0TG9hZEVycm9yLFxyXG4gICAgb3B0aW9ucyxcclxuICBdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxSZWFjdC5GcmFnbWVudD5cclxuICAgICAge2xvYWRpbmcgJiYgcGxhY2Vob2xkZXJ9XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidHdpdHRlci1lbWJlZFwiIHJlZj17cmVmfSB7Li4ucmVzdH0+PC9kaXY+XHJcbiAgICA8L1JlYWN0LkZyYWdtZW50PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVRoZW1lIiwiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZVNjcmlwdCIsInNyYyIsInN0YXR1cyIsInNldFN0YXR1cyIsInNjcmlwdCIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvciIsImNyZWF0ZUVsZW1lbnQiLCJhc3luYyIsInNldEF0dHJpYnV0ZSIsImJvZHkiLCJhcHBlbmRDaGlsZCIsInNldEF0dHJpYnV0ZUZyb21FdmVudCIsImV2ZW50IiwidHlwZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJnZXRBdHRyaWJ1dGUiLCJzZXRTdGF0ZUZyb21FdmVudCIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJUV0lUVEVSX1dJREdFVF9VUkwiLCJFbWJlZFR3ZWV0IiwidHdlZXRJZCIsImxpYlVybCIsIm9wdGlvbnMiLCJwbGFjZWhvbGRlciIsIm9uVHdlZXRMb2FkU3VjY2VzcyIsIm9uVHdlZXRMb2FkRXJyb3IiLCJyZXN0IiwicmVmIiwic2NyaXB0U3RhdHVzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJ0aGVtZSIsInN5c3RlbVRoZW1lIiwiY3VycmVudFRoZW1lIiwiZWwiLCJjdXJyZW50IiwiaXNDb21wb25lbnRNb3VudGVkIiwid2luZG93IiwicmVhZHkiLCJ0aGVuIiwid2lkZ2V0cyIsImNyZWF0ZVR3ZWV0IiwiY2FyZHMiLCJ0d2l0dGVyV2lkZ2V0RWxlbWVudCIsImNhdGNoIiwiRnJhZ21lbnQiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/TweetEmbed.tsx\n");

/***/ })

};
;