"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Quiz_QuizPlayBodyRenderer_tsx";
exports.ids = ["components_Tasks_Quiz_QuizPlayBodyRenderer_tsx"];
exports.modules = {

/***/ "./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useRouterQuery */ \"./hooks/useRouterQuery.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_OptionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/OptionList */ \"./components/Tasks/Quiz/components/OptionList.tsx\");\n/* harmony import */ var _components_Question__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Question */ \"./components/Tasks/Quiz/components/Question.tsx\");\n/* harmony import */ var _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/useQuizData */ \"./components/Tasks/Quiz/components/useQuizData.ts\");\n/* harmony import */ var _quiz_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./quiz.gql */ \"./components/Tasks/Quiz/quiz.gql.ts\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _components_OptionList__WEBPACK_IMPORTED_MODULE_4__, _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__, _quiz_gql__WEBPACK_IMPORTED_MODULE_7__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__]);\n([_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__, _components_OptionList__WEBPACK_IMPORTED_MODULE_4__, _components_useQuizData__WEBPACK_IMPORTED_MODULE_6__, _quiz_gql__WEBPACK_IMPORTED_MODULE_7__, _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nconst QuizPlayBodyRenderer = ({ projectEventId, task, onSuccess, questions, completedCount })=>{\n    const [participateQuiz, { loading: verifying }] = (0,_quiz_gql__WEBPACK_IMPORTED_MODULE_7__.useParticipateQuiz)();\n    const { value, set, clear } = (0,_Hooks_useRouterQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"question\");\n    const currentQuestionIndex = parseInt(value || \"0\");\n    const currentQuestion = questions ? questions[currentQuestionIndex] : undefined;\n    const { id, description } = task;\n    const [optionsSelected, setOptionsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const [loading, questionsState] = (0,_components_useQuizData__WEBPACK_IMPORTED_MODULE_6__.useQuizData)(projectEventId, questions);\n    const handleNavigate = (step)=>{\n        const index = currentQuestionIndex + step;\n        if (index < questions.length || index >= 0) {\n            set(index.toString());\n        }\n        setOptionsSelected([]);\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const lastCompletedCount = completedCount;\n        if (!optionsSelected?.length) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                title: \"Please select an answer\",\n                type: \"warning\"\n            });\n            return;\n        }\n        participateQuiz({\n            variables: {\n                eventId: projectEventId,\n                taskId: currentQuestion?.id || \"\",\n                data: {\n                    answers: optionsSelected\n                }\n            },\n            context: {\n                taskParentId: id\n            },\n            onCompleted: ()=>{\n                //If this is the last question, then send onSuccess\n                if (lastCompletedCount && lastCompletedCount + 1 == questions.length) {\n                    clear();\n                    return onSuccess?.();\n                }\n            },\n            onError: (error)=>{\n                (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                    title: \"Error!\",\n                    defaultText: \"Error verifying answer!\",\n                    type: \"error\",\n                    error\n                });\n            }\n        });\n    };\n    const handleChange = ()=>{\n        const form_data = new FormData(formRef.current);\n        const answers = Array.from(form_data.values()).map((el)=>{\n            return el;\n        });\n        setOptionsSelected(answers);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                lineNumber: 98,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                ref: formRef,\n                onChange: handleChange,\n                children: [\n                    currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"block w-full space-y-6 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Question__WEBPACK_IMPORTED_MODULE_5__.Question, {\n                                question: currentQuestion,\n                                questionNumber: currentQuestionIndex + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionList__WEBPACK_IMPORTED_MODULE_4__.OptionList, {\n                                currentQuestion: currentQuestion,\n                                selectedOptions: questionsState?.get(currentQuestion.id)?.selectedOptions,\n                                submitted: questionsState?.has(currentQuestion.id),\n                                correctOptions: questionsState?.get(currentQuestion.id)?.correctOptions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-4 mt-8\",\n                        children: [\n                            currentQuestionIndex !== questions.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                rounded: \"full\",\n                                variant: !questionsState?.has(currentQuestion?.id || \"\") ? \"outline\" : \"default\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    handleNavigate(1);\n                                },\n                                children: questionsState?.has(currentQuestion?.id || \"\") ? \"Next\" : \"Skip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                rounded: \"full\",\n                                loading: verifying || loading,\n                                disabled: questionsState?.has(currentQuestion?.id || \"\") || !optionsSelected.length,\n                                type: \"submit\",\n                                children: !questionsState?.has(currentQuestion?.id || \"\") ? \"Submit Answer\" : \"Submitted\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\QuizPlayBodyRenderer.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizPlayBodyRenderer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/Option.tsx":
/*!*****************************************************!*\
  !*** ./components/Tasks/Quiz/components/Option.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Option: () => (/* binding */ Option)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction Option({ option: { id, text }, questionType, name, answered, isSelected, isCorrect, hasCorrectOption }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-1 border rounded-md relative flex items-center component-bg`, isSelected ? \"border-primary\" : \"\", answered ? \"cursor-not-allowed\" : \"component-bg-hover cursor-pointer\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(questionType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.QuizQuestionType.SINGLE_CHOICE ? \"rounded-full\" : \"rounded-sm\", `ml-4 mr-2 appearance-none h-4 w-4 border bg-foreground/20 border-input checked:bg-primary checked:border-primary cursor-pointer focus:outline-none transition duration-200`),\n                type: questionType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.QuizQuestionType.SINGLE_CHOICE ? \"radio\" : \"checkbox\",\n                id: id,\n                name: name,\n                value: id,\n                defaultChecked: isSelected,\n                disabled: answered\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: id,\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex-1 inline-flex items-center py-2 pr-6\", answered ? \"cursor-not-allowed\" : \"cursor-pointer\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-4 inline-block\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    answered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute text-xl right-2\",\n                        children: isCorrect ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                            className: \"text-primary inline\",\n                            weight: \"fill\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 15\n                        }, this) : isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.XCircle, {\n                            className: \"text-red-500 inline\",\n                            weight: \"fill\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Option.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/Option.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/OptionList.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/Quiz/components/OptionList.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionList: () => (/* binding */ OptionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Option__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Option */ \"./components/Tasks/Quiz/components/Option.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Option__WEBPACK_IMPORTED_MODULE_1__]);\n_Option__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction OptionList({ currentQuestion, selectedOptions, submitted, correctOptions }) {\n    const quizTaskData = currentQuestion.info;\n    const isOptionSelected = (optionId)=>!!selectedOptions?.find((id)=>id === optionId);\n    const isCorrectOption = (optionId)=>{\n        if (!correctOptions) {\n            return (correctOptions || false) && isOptionSelected(optionId);\n        }\n        return correctOptions.includes(optionId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 mb-4\",\n        children: quizTaskData.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Option__WEBPACK_IMPORTED_MODULE_1__.Option, {\n                option: option,\n                name: currentQuestion.id,\n                questionType: quizTaskData.questionType,\n                isCorrect: isCorrectOption(option.id),\n                isSelected: isOptionSelected(option.id),\n                answered: submitted\n            }, option.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\OptionList.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\OptionList.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/OptionList.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/Question.tsx":
/*!*******************************************************!*\
  !*** ./components/Tasks/Quiz/components/Question.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Question: () => (/* binding */ Question)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Question({ question: { title }, questionNumber }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-ch flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs bg-foreground/10 border border-foreground/10 rounded-full px-2 py-1\",\n                children: [\n                    \"Q\",\n                    questionNumber\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Quiz\\\\components\\\\Question.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1Rhc2tzL1F1aXovY29tcG9uZW50cy9RdWVzdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBLFNBQVMsRUFDdkJDLFVBQVUsRUFBRUMsS0FBSyxFQUFFLEVBQ25CQyxjQUFjLEVBSWY7SUFDQyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFLRCxXQUFVOztvQkFBOEU7b0JBQzFGRjs7Ozs7OzswQkFFSiw4REFBQ0c7MEJBQU1KOzs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvVGFza3MvUXVpei9jb21wb25lbnRzL1F1ZXN0aW9uLnRzeD9mZjJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRhc2sgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUXVlc3Rpb24oe1xyXG4gIHF1ZXN0aW9uOiB7IHRpdGxlIH0sXHJcbiAgcXVlc3Rpb25OdW1iZXIsXHJcbn06IHtcclxuICBxdWVzdGlvbjogVGFzaztcclxuICBxdWVzdGlvbk51bWJlcjogbnVtYmVyO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jaCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWZvcmVncm91bmQvMTAgYm9yZGVyIGJvcmRlci1mb3JlZ3JvdW5kLzEwIHJvdW5kZWQtZnVsbCBweC0yIHB5LTFcIj5cclxuICAgICAgICBRe3F1ZXN0aW9uTnVtYmVyfVxyXG4gICAgICA8L3NwYW4+XHJcbiAgICAgIDxzcGFuPnt0aXRsZX08L3NwYW4+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJRdWVzdGlvbiIsInF1ZXN0aW9uIiwidGl0bGUiLCJxdWVzdGlvbk51bWJlciIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/Question.tsx\n");

/***/ }),

/***/ "./components/Tasks/Quiz/components/useQuizData.ts":
/*!*********************************************************!*\
  !*** ./components/Tasks/Quiz/components/useQuizData.ts ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuizData: () => (/* binding */ useQuizData)\n/* harmony export */ });\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__]);\n_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction useQuizData(projectEventId, questions) {\n    const { data: userTaskParticipation, loading: isParticipationLoading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_0__.useUserCurrentTaskParticipationMap)(projectEventId);\n    return [\n        isParticipationLoading,\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n            let mapTemp = new Map();\n            if (userTaskParticipation) {\n                userTaskParticipation.forEach((item)=>{\n                    if (questions.filter((i)=>i.id === item.taskId).length > 0) {\n                        mapTemp.set(item.taskId, {\n                            selectedOptions: item.info.answers,\n                            correctOptions: item.info.correctAnswers\n                        });\n                    }\n                });\n            }\n            return mapTemp;\n        }, [\n            userTaskParticipation,\n            questions\n        ])\n    ];\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Quiz/components/useQuizData.ts\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MediaDisplay({ block, contentState }) {\n    const entityKey = block.getEntityAt(0);\n    if (!entityKey) return null;\n    const entity = contentState.getEntity(entityKey);\n    const { src, mediaType } = entity.getData();\n    if (!src) return null;\n    if (mediaType === \"video\" || entity.getType() === \"VIDEO\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n            controls: true,\n            preload: \"metadata\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\",\n                borderRadius: \"6px\",\n                margin: \"8px 0\"\n            },\n            src: src,\n            children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: \"Rich Text Image\",\n        style: {\n            maxWidth: \"100%\",\n            height: \"auto\",\n            borderRadius: \"6px\",\n            margin: \"8px 0\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction mediaBlockRenderer(block) {\n    if (block.getType() === \"atomic\") {\n        return {\n            component: MediaDisplay,\n            editable: false\n        };\n    }\n    return null;\n}\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_4__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_4__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_4__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                className: \"jsx-7a4dfc642d828fbd\" + \" \" + ((0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\") || \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"7a4dfc642d828fbd\",\n                        children: \".RichEditor-text-align-left,.RichEditor-text-align-left .public-DraftStyleDefault-block{text-align:left!important}.RichEditor-text-align-center,.RichEditor-text-align-center .public-DraftStyleDefault-block{text-align:center!important}.RichEditor-text-align-right,.RichEditor-text-align-right .public-DraftStyleDefault-block{text-align:right!important}\"\n                    }, void 0, false, void 0, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_4__.Editor, {\n                        editorState: editorState,\n                        readOnly: readonly,\n                        onChange: ()=>{},\n                        blockRendererFn: mediaBlockRenderer,\n                        blockStyleFn: (block)=>{\n                            const blockData = block.getData();\n                            const textAlign = blockData.get(\"textAlign\") || \"left\";\n                            let className = \"\";\n                            switch(block.getType()){\n                                case \"blockquote\":\n                                    className = \"RichEditor-blockquote\";\n                                    break;\n                                default:\n                                    className = \"\";\n                            }\n                            className += ` RichEditor-text-align-${textAlign}`;\n                            return className.trim();\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;