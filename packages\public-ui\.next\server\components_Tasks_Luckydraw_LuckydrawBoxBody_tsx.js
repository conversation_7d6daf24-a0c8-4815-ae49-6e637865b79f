"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Tasks_Luckydraw_LuckydrawBoxBody_tsx";
exports.ids = ["components_Tasks_Luckydraw_LuckydrawBoxBody_tsx"];
exports.modules = {

/***/ "./components/MysteryBox.tsx":
/*!***********************************!*\
  !*** ./components/MysteryBox.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MysteryBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! canvas-confetti */ \"canvas-confetti\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(canvas_confetti__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction MysteryBox({ rewards = [], onBoxStart = ()=>{}, onBoxEnd = ()=>{}, resultIndex = null, isShaking = false, rewardType }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [hasOpened, setHasOpened] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [openingStage, setOpeningStage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [lidAngle, setLidAngle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [rewardY, setRewardY] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [rewardOpacity, setRewardOpacity] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const animationStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(0);\n    const shakeOffsetRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const bounceOffsetRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(0);\n    // Generate random particles for the background\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const newParticles = Array.from({\n            length: 30\n        }, ()=>({\n                x: Math.random() * 100,\n                y: Math.random() * 100,\n                size: Math.random() * 4 + 1,\n                color: `hsl(${Math.random() * 360}, 80%, 70%)`,\n                speed: Math.random() * 0.5 + 0.1\n            }));\n        setParticles(newParticles);\n    }, []);\n    // Handle automatic opening when resultIndex is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (resultIndex !== null && !isOpen) {\n            handleOpen();\n        }\n    }, [\n        resultIndex,\n        isOpen\n    ]);\n    // Initialize with result if available immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (resultIndex !== null) {\n            setIsOpen(true);\n            setOpeningStage(2);\n            setRewardY(0);\n            setRewardOpacity(1);\n        }\n    }, []);\n    const handleOpen = ()=>{\n        if (!isOpen) {\n            onBoxStart();\n            setIsOpen(true);\n            setHasOpened(true);\n            setOpeningStage(1);\n            animationStartTimeRef.current = performance.now();\n            // Trigger confetti explosion\n            canvas_confetti__WEBPACK_IMPORTED_MODULE_3___default()({\n                particleCount: 150,\n                spread: 100,\n                origin: {\n                    y: 0.6\n                },\n                colors: [\n                    \"#FFD700\",\n                    \"#FFC0CB\",\n                    \"#9370DB\",\n                    \"#00BFFF\",\n                    \"#7CFC00\"\n                ]\n            });\n            // Simulate opening animation timing\n            setTimeout(()=>{\n                setOpeningStage(2);\n                setRewardY(0);\n                setRewardOpacity(1);\n                onBoxEnd();\n            }, 600);\n        } else {\n            setIsOpen(false);\n        }\n    };\n    // Calculate shake effect if isShaking is true\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (isShaking && !isOpen) {\n            const interval = setInterval(()=>{\n                shakeOffsetRef.current = {\n                    x: (Math.random() - 0.5) * 8,\n                    y: (Math.random() - 0.5) * 8\n                };\n                // Add slight bounce effect\n                bounceOffsetRef.current = -Math.abs(Math.sin(Date.now() / 200) * 5);\n            }, 50);\n            return ()=>clearInterval(interval);\n        } else {\n            shakeOffsetRef.current = {\n                x: 0,\n                y: 0\n            };\n            bounceOffsetRef.current = 0;\n        }\n    }, [\n        isShaking,\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-be46807299b558d2\" + \" \" + \"relative flex flex-col items-center justify-center min-h-[500px] p-4 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-be46807299b558d2\" + \" \" + \"absolute inset-0 overflow-hidden\",\n                children: particles.map((particle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: particle.size,\n                            height: particle.size,\n                            backgroundColor: particle.color,\n                            top: `${particle.y}%`,\n                            left: `${particle.x}%`,\n                            filter: \"blur(1px)\",\n                            animationDuration: `${10 / particle.speed}s`\n                        },\n                        className: \"jsx-be46807299b558d2\" + \" \" + \"absolute rounded-full animate-float\"\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"jsx-be46807299b558d2\" + \" \" + \"text-4xl font-bold mb-8 text-center text-white drop-shadow-[0_0_15px_rgba(255,255,255,0.5)] relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"jsx-be46807299b558d2\" + \" \" + \"bg-clip-text text-transparent bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500\",\n                    children: \"Mystery Box\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-be46807299b558d2\" + \" \" + \"relative w-full max-w-sm z-10\",\n                children: !isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        transform: `translate(${shakeOffsetRef.current.x}px, ${shakeOffsetRef.current.y + bounceOffsetRef.current}px)`\n                    },\n                    className: \"jsx-be46807299b558d2\" + \" \" + `transition-all duration-500 ${isOpen ? \"opacity-0 scale-125 rotate-y-90\" : \"opacity-100 scale-100\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-be46807299b558d2\" + \" \" + \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-be46807299b558d2\" + \" \" + \"absolute -inset-1 rounded-xl bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 opacity-75 blur-lg group-hover:opacity-100 transition duration-1000 animate-gradient-x\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-be46807299b558d2\" + \" \" + \"relative overflow-hidden bg-black/80 border-0 backdrop-blur-sm shadow-[0_0_25px_rgba(123,31,162,0.5)] rounded-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-be46807299b558d2\" + \" \" + \"p-10 flex flex-col items-center justify-center min-h-[350px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-be46807299b558d2\" + \" \" + \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"relative w-32 h-32 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"translateZ(16px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-600 to-fuchsia-600 rounded-lg shadow-inner transform-gpu animate-pulse-shadow\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-be46807299b558d2\" + \" \" + \"absolute inset-0 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"animate-spin-slow\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__.StarIcon, {\n                                                                        className: \"h-12 w-12 text-yellow-300/80\",\n                                                                        strokeWidth: 1,\n                                                                        fill: \"rgba(253, 224, 71, 0.3)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"rotateX(90deg) translateZ(16px) translateY(16px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-violet-600 to-indigo-600 rounded-lg origin-bottom transform-gpu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"rotateY(-90deg) translateZ(16px) translateX(-16px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-fuchsia-700 to-purple-700 rounded-lg origin-right transform-gpu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"rotateY(90deg) translateZ(16px) translateX(16px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-fuchsia-700 to-purple-700 rounded-lg origin-left transform-gpu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"translateZ(32px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 left-0 right-0 h-4 bg-pink-500 transform-gpu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                transform: \"translateZ(32px)\"\n                                                            },\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-0 bottom-0 left-0 w-4 bg-pink-500 transform-gpu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"absolute -top-6 -right-6 animate-float\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__.SparklesIcon, {\n                                                        className: \"h-8 w-8 text-yellow-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"absolute -bottom-4 -left-4 animate-float-delay\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__.SparklesIcon, {\n                                                        className: \"h-6 w-6 text-cyan-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-be46807299b558d2\" + \" \" + \"text-xl font-medium mt-4 text-transparent bg-clip-text bg-gradient-to-r from-pink-300 via-purple-300 to-indigo-300 animate-float-text\",\n                                            children: \"Use the button below to open\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute inset-0 rounded-xl bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-indigo-500/20 animate-pulse-opacity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-be46807299b558d2\" + \" \" + `transition-all duration-500 ${!isOpen ? \"opacity-0 scale-80 -rotate-y-90\" : \"opacity-100 scale-100\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-be46807299b558d2\" + \" \" + \"relative overflow-hidden border-0 bg-gradient-to-br from-indigo-900/90 via-purple-900/90 to-fuchsia-900/90 backdrop-blur-md shadow-[0_0_30px_rgba(139,92,246,0.7)] rounded-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-be46807299b558d2\" + \" \" + \"p-10 flex flex-col items-center justify-center min-h-[350px] relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-be46807299b558d2\" + \" \" + \"absolute inset-0 overflow-hidden\",\n                                    children: Array.from({\n                                        length: 8\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                transform: `rotate(${i * 45}deg)`,\n                                                animationDelay: `${i * 0.2}s`\n                                            },\n                                            className: \"jsx-be46807299b558d2\" + \" \" + \"absolute top-1/2 left-1/2 w-[500px] h-2 bg-gradient-to-r from-transparent via-pink-500/30 to-transparent animate-ray\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-be46807299b558d2\" + \" \" + \"relative mb-6 animate-reveal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-be46807299b558d2\" + \" \" + \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-be46807299b558d2\" + \" \" + \"absolute -inset-6 rounded-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 opacity-30 blur-md animate-spin-slow\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-be46807299b558d2\" + \" \" + \"relative z-10 animate-pulse-scale\",\n                                                children: resultIndex !== null && resultIndex !== undefined && rewards ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        opacity: rewardOpacity,\n                                                        transform: `translateY(${rewardY}%)`,\n                                                        transition: \"transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.6s ease\"\n                                                    },\n                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"w-24 h-24 rounded-full bg-gradient-to-br from-yellow-300 via-amber-400 to-yellow-500 flex items-center justify-center shadow-[0_0_30px_rgba(250,204,21,0.7)]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-be46807299b558d2\" + \" \" + \"text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-be46807299b558d2\" + \" \" + \"text-xl font-bold text-yellow-100\",\n                                                            children: rewards[resultIndex] ? `${rewards[resultIndex]?.amount} ${rewardType}` : \"Prize\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-be46807299b558d2\" + \" \" + \"w-24 h-24 rounded-full bg-gradient-to-br from-yellow-300 via-amber-400 to-yellow-500 flex items-center justify-center shadow-[0_0_30px_rgba(250,204,21,0.7)]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__.StarIcon, {\n                                                        className: \"h-12 w-12 text-yellow-100\",\n                                                        fill: \"rgba(255, 255, 255, 0.8)\",\n                                                        strokeWidth: 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-be46807299b558d2\" + \" \" + \"text-center relative z-10 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-be46807299b558d2\" + \" \" + \"text-3xl font-bold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-pink-300 via-purple-300 to-indigo-300 animate-fade-in-delay-1\",\n                                        children: \"✨ Magnificent! ✨\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"be46807299b558d2\",\n                children: \"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0);transform:translatey(0);opacity:.4}50%{-webkit-transform:translatey(-10px);transform:translatey(-10px);opacity:.8}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0);transform:translatey(0);opacity:.4}50%{-moz-transform:translatey(-10px);transform:translatey(-10px);opacity:.8}}@-o-keyframes float{0%,100%{-o-transform:translatey(0);transform:translatey(0);opacity:.4}50%{-o-transform:translatey(-10px);transform:translatey(-10px);opacity:.8}}@keyframes float{0%,100%{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:.4}50%{-webkit-transform:translatey(-10px);-moz-transform:translatey(-10px);-o-transform:translatey(-10px);transform:translatey(-10px);opacity:.8}}@-webkit-keyframes spin-slow{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin-slow{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin-slow{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin-slow{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes pulse-shadow{0%,100%{-webkit-box-shadow:0 0 20px rgba(192,132,252,.5)inset;box-shadow:0 0 20px rgba(192,132,252,.5)inset}50%{-webkit-box-shadow:0 0 30px rgba(192,132,252,.8)inset;box-shadow:0 0 30px rgba(192,132,252,.8)inset}}@-moz-keyframes pulse-shadow{0%,100%{-moz-box-shadow:0 0 20px rgba(192,132,252,.5)inset;box-shadow:0 0 20px rgba(192,132,252,.5)inset}50%{-moz-box-shadow:0 0 30px rgba(192,132,252,.8)inset;box-shadow:0 0 30px rgba(192,132,252,.8)inset}}@-o-keyframes pulse-shadow{0%,100%{box-shadow:0 0 20px rgba(192,132,252,.5)inset}50%{box-shadow:0 0 30px rgba(192,132,252,.8)inset}}@keyframes pulse-shadow{0%,100%{-webkit-box-shadow:0 0 20px rgba(192,132,252,.5)inset;-moz-box-shadow:0 0 20px rgba(192,132,252,.5)inset;box-shadow:0 0 20px rgba(192,132,252,.5)inset}50%{-webkit-box-shadow:0 0 30px rgba(192,132,252,.8)inset;-moz-box-shadow:0 0 30px rgba(192,132,252,.8)inset;box-shadow:0 0 30px rgba(192,132,252,.8)inset}}@-webkit-keyframes pulse-opacity{0%,100%{opacity:0;-webkit-transform:scale(.8);transform:scale(.8)}50%{opacity:.5;-webkit-transform:scale(1.05);transform:scale(1.05)}}@-moz-keyframes pulse-opacity{0%,100%{opacity:0;-moz-transform:scale(.8);transform:scale(.8)}50%{opacity:.5;-moz-transform:scale(1.05);transform:scale(1.05)}}@-o-keyframes pulse-opacity{0%,100%{opacity:0;-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.5;-o-transform:scale(1.05);transform:scale(1.05)}}@keyframes pulse-opacity{0%,100%{opacity:0;-webkit-transform:scale(.8);-moz-transform:scale(.8);-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.5;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}}@-webkit-keyframes float-text{0%,100%{-webkit-transform:translatey(0);transform:translatey(0);opacity:.8}50%{-webkit-transform:translatey(-5px);transform:translatey(-5px);opacity:1}}@-moz-keyframes float-text{0%,100%{-moz-transform:translatey(0);transform:translatey(0);opacity:.8}50%{-moz-transform:translatey(-5px);transform:translatey(-5px);opacity:1}}@-o-keyframes float-text{0%,100%{-o-transform:translatey(0);transform:translatey(0);opacity:.8}50%{-o-transform:translatey(-5px);transform:translatey(-5px);opacity:1}}@keyframes float-text{0%,100%{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:.8}50%{-webkit-transform:translatey(-5px);-moz-transform:translatey(-5px);-o-transform:translatey(-5px);transform:translatey(-5px);opacity:1}}@-webkit-keyframes gradient-x{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradient-x{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradient-x{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradient-x{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-webkit-keyframes ray{0%{opacity:0;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}50%{opacity:.7;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(1)rotate(inherit);transform:scalex(1)rotate(inherit)}100%{opacity:0;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}}@-moz-keyframes ray{0%{opacity:0;-moz-transform-origin:0 0;transform-origin:0 0;-moz-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}50%{opacity:.7;-moz-transform-origin:0 0;transform-origin:0 0;-moz-transform:scalex(1)rotate(inherit);transform:scalex(1)rotate(inherit)}100%{opacity:0;-moz-transform-origin:0 0;transform-origin:0 0;-moz-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}}@-o-keyframes ray{0%{opacity:0;-o-transform-origin:0 0;transform-origin:0 0;-o-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}50%{opacity:.7;-o-transform-origin:0 0;transform-origin:0 0;-o-transform:scalex(1)rotate(inherit);transform:scalex(1)rotate(inherit)}100%{opacity:0;-o-transform-origin:0 0;transform-origin:0 0;-o-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}}@keyframes ray{0%{opacity:0;-webkit-transform-origin:0 0;-moz-transform-origin:0 0;-o-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(0)rotate(inherit);-moz-transform:scalex(0)rotate(inherit);-o-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}50%{opacity:.7;-webkit-transform-origin:0 0;-moz-transform-origin:0 0;-o-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(1)rotate(inherit);-moz-transform:scalex(1)rotate(inherit);-o-transform:scalex(1)rotate(inherit);transform:scalex(1)rotate(inherit)}100%{opacity:0;-webkit-transform-origin:0 0;-moz-transform-origin:0 0;-o-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scalex(0)rotate(inherit);-moz-transform:scalex(0)rotate(inherit);-o-transform:scalex(0)rotate(inherit);transform:scalex(0)rotate(inherit)}}@-webkit-keyframes reveal{from{-webkit-transform:scale(0)rotate(-180deg);transform:scale(0)rotate(-180deg)}to{-webkit-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}}@-moz-keyframes reveal{from{-moz-transform:scale(0)rotate(-180deg);transform:scale(0)rotate(-180deg)}to{-moz-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}}@-o-keyframes reveal{from{-o-transform:scale(0)rotate(-180deg);transform:scale(0)rotate(-180deg)}to{-o-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}}@keyframes reveal{from{-webkit-transform:scale(0)rotate(-180deg);-moz-transform:scale(0)rotate(-180deg);-o-transform:scale(0)rotate(-180deg);transform:scale(0)rotate(-180deg)}to{-webkit-transform:scale(1)rotate(0);-moz-transform:scale(1)rotate(0);-o-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}}@-webkit-keyframes pulse-scale{0%,100%{-webkit-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}33%{-webkit-transform:scale(1.2)rotate(5deg);transform:scale(1.2)rotate(5deg)}66%{-webkit-transform:scale(1.2)rotate(-5deg);transform:scale(1.2)rotate(-5deg)}}@-moz-keyframes pulse-scale{0%,100%{-moz-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}33%{-moz-transform:scale(1.2)rotate(5deg);transform:scale(1.2)rotate(5deg)}66%{-moz-transform:scale(1.2)rotate(-5deg);transform:scale(1.2)rotate(-5deg)}}@-o-keyframes pulse-scale{0%,100%{-o-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}33%{-o-transform:scale(1.2)rotate(5deg);transform:scale(1.2)rotate(5deg)}66%{-o-transform:scale(1.2)rotate(-5deg);transform:scale(1.2)rotate(-5deg)}}@keyframes pulse-scale{0%,100%{-webkit-transform:scale(1)rotate(0);-moz-transform:scale(1)rotate(0);-o-transform:scale(1)rotate(0);transform:scale(1)rotate(0)}33%{-webkit-transform:scale(1.2)rotate(5deg);-moz-transform:scale(1.2)rotate(5deg);-o-transform:scale(1.2)rotate(5deg);transform:scale(1.2)rotate(5deg)}66%{-webkit-transform:scale(1.2)rotate(-5deg);-moz-transform:scale(1.2)rotate(-5deg);-o-transform:scale(1.2)rotate(-5deg);transform:scale(1.2)rotate(-5deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}@-webkit-keyframes rotate-y{from{-webkit-transform:rotatey(0deg);transform:rotatey(0deg)}to{-webkit-transform:rotatey(90deg);transform:rotatey(90deg)}}@-moz-keyframes rotate-y{from{-moz-transform:rotatey(0deg);transform:rotatey(0deg)}to{-moz-transform:rotatey(90deg);transform:rotatey(90deg)}}@-o-keyframes rotate-y{from{-o-transform:rotatey(0deg);transform:rotatey(0deg)}to{-o-transform:rotatey(90deg);transform:rotatey(90deg)}}@keyframes rotate-y{from{-webkit-transform:rotatey(0deg);-moz-transform:rotatey(0deg);-o-transform:rotatey(0deg);transform:rotatey(0deg)}to{-webkit-transform:rotatey(90deg);-moz-transform:rotatey(90deg);-o-transform:rotatey(90deg);transform:rotatey(90deg)}}.animate-float.jsx-be46807299b558d2{-webkit-animation:float 3s ease-in-out infinite;-moz-animation:float 3s ease-in-out infinite;-o-animation:float 3s ease-in-out infinite;animation:float 3s ease-in-out infinite}.animate-float-delay.jsx-be46807299b558d2{-webkit-animation:float 2.5s ease-in-out.5s infinite;-moz-animation:float 2.5s ease-in-out.5s infinite;-o-animation:float 2.5s ease-in-out.5s infinite;animation:float 2.5s ease-in-out.5s infinite}.animate-spin-slow.jsx-be46807299b558d2{-webkit-animation:spin-slow 20s linear infinite;-moz-animation:spin-slow 20s linear infinite;-o-animation:spin-slow 20s linear infinite;animation:spin-slow 20s linear infinite}.animate-pulse-shadow.jsx-be46807299b558d2{-webkit-animation:pulse-shadow 2s ease-in-out infinite;-moz-animation:pulse-shadow 2s ease-in-out infinite;-o-animation:pulse-shadow 2s ease-in-out infinite;animation:pulse-shadow 2s ease-in-out infinite}.animate-pulse-opacity.jsx-be46807299b558d2{-webkit-animation:pulse-opacity 3s ease-in-out infinite;-moz-animation:pulse-opacity 3s ease-in-out infinite;-o-animation:pulse-opacity 3s ease-in-out infinite;animation:pulse-opacity 3s ease-in-out infinite}.animate-float-text.jsx-be46807299b558d2{-webkit-animation:float-text 3s ease-in-out infinite;-moz-animation:float-text 3s ease-in-out infinite;-o-animation:float-text 3s ease-in-out infinite;animation:float-text 3s ease-in-out infinite}.animate-gradient-x.jsx-be46807299b558d2{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradient-x 8s linear infinite;-moz-animation:gradient-x 8s linear infinite;-o-animation:gradient-x 8s linear infinite;animation:gradient-x 8s linear infinite}.animate-ray.jsx-be46807299b558d2{-webkit-animation:ray 3s ease-in-out infinite;-moz-animation:ray 3s ease-in-out infinite;-o-animation:ray 3s ease-in-out infinite;animation:ray 3s ease-in-out infinite}.animate-reveal.jsx-be46807299b558d2{-webkit-animation:reveal.6s cubic-bezier(.175,.885,.32,1.275).2s both;-moz-animation:reveal.6s cubic-bezier(.175,.885,.32,1.275).2s both;-o-animation:reveal.6s cubic-bezier(.175,.885,.32,1.275).2s both;animation:reveal.6s cubic-bezier(.175,.885,.32,1.275).2s both}.animate-pulse-scale.jsx-be46807299b558d2{-webkit-animation:pulse-scale 4s ease-in-out infinite;-moz-animation:pulse-scale 4s ease-in-out infinite;-o-animation:pulse-scale 4s ease-in-out infinite;animation:pulse-scale 4s ease-in-out infinite}.animate-fade-in.jsx-be46807299b558d2{-webkit-animation:fade-in.5s ease-out.5s both;-moz-animation:fade-in.5s ease-out.5s both;-o-animation:fade-in.5s ease-out.5s both;animation:fade-in.5s ease-out.5s both}.animate-fade-in-delay-1.jsx-be46807299b558d2{-webkit-animation:fade-in.5s ease-out.7s both;-moz-animation:fade-in.5s ease-out.7s both;-o-animation:fade-in.5s ease-out.7s both;animation:fade-in.5s ease-out.7s both}.animate-fade-in-delay-2.jsx-be46807299b558d2{-webkit-animation:fade-in.5s ease-out.9s both;-moz-animation:fade-in.5s ease-out.9s both;-o-animation:fade-in.5s ease-out.9s both;animation:fade-in.5s ease-out.9s both}.animate-fade-in-delay-3.jsx-be46807299b558d2{-webkit-animation:fade-in.5s ease-out 1.1s both;-moz-animation:fade-in.5s ease-out 1.1s both;-o-animation:fade-in.5s ease-out 1.1s both;animation:fade-in.5s ease-out 1.1s both}.rotate-y-90.jsx-be46807299b558d2{-webkit-transform:rotatey(90deg);-moz-transform:rotatey(90deg);-ms-transform:rotatey(90deg);-o-transform:rotatey(90deg);transform:rotatey(90deg)}.-rotate-y-90.jsx-be46807299b558d2{-webkit-transform:rotatey(-90deg);-moz-transform:rotatey(-90deg);-ms-transform:rotatey(-90deg);-o-transform:rotatey(-90deg);transform:rotatey(-90deg)}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\MysteryBox.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/MysteryBox.tsx\n");

/***/ }),

/***/ "./components/Tasks/Luckydraw/LuckydrawBoxBody.tsx":
/*!*********************************************************!*\
  !*** ./components/Tasks/Luckydraw/LuckydrawBoxBody.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Apps/components/TaskCompletedCard */ \"./components/Tasks/components/TaskCompletedCard.tsx\");\n/* harmony import */ var _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./luckydraw.gql */ \"./components/Tasks/Luckydraw/luckydraw.gql.ts\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _Components_MysteryBox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Components/MysteryBox */ \"./components/MysteryBox.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__]);\n([_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__, _luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst LuckydrawBoxBody = ({ projectEventId, task, verified, onError, scoredPoints, taskParticipation, project })=>{\n    const [isParticipating, setIsParticipating] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [boxState, setBoxState] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        isOpening: false,\n        isShaking: false,\n        resultIndex: taskParticipation?.info?.resultIndex ?? null\n    });\n    const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(verified && !boxState.isOpening);\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const { id, description } = task;\n    const taskInfo = task.info;\n    const { rewardType, rewards } = taskInfo;\n    const rewardTypes = rewardType === _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.RewardType.POINTS ? `${globalT(\"projectPoints\")}` : `${globalT(\"projectXp\")}`;\n    const [participateLuckydraw] = (0,_luckydraw_gql__WEBPACK_IMPORTED_MODULE_2__.useParticipateLuckydraw)();\n    const handleBoxComplete = ()=>{\n        setBoxState((prev)=>({\n                ...prev,\n                isOpening: false\n            }));\n        setShowResult(true);\n        setIsParticipating(false);\n    };\n    const handleClick = async ()=>{\n        if (boxState.isOpening) return;\n        try {\n            // Start shaking animation\n            setBoxState((prev)=>({\n                    ...prev,\n                    isShaking: true\n                }));\n            // After shaking, start opening\n            setTimeout(async ()=>{\n                setBoxState((prev)=>({\n                        ...prev,\n                        isShaking: false,\n                        isOpening: true\n                    }));\n                setIsParticipating(true);\n                setShowResult(false);\n                const response = await participateLuckydraw({\n                    variables: {\n                        eventId: projectEventId,\n                        taskId: id\n                    },\n                    context: {\n                        rewardType\n                    }\n                });\n                setBoxState((prev)=>({\n                        ...prev,\n                        resultIndex: response?.data?.participateLuckydrawTask?.resultIndex ?? 0\n                    }));\n            }, 1000);\n        } catch (error) {\n            setBoxState((prev)=>({\n                    ...prev,\n                    isOpening: false,\n                    isShaking: false\n                }));\n            setIsParticipating(false);\n            (0,_components_TaskToaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                title: \"Error!\",\n                defaultText: \"Error opening the box!\",\n                type: \"error\",\n                error\n            });\n            onError?.();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                defaultValue: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n                lineNumber: 110,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_MysteryBox__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    rewards: rewards,\n                    onBoxStart: ()=>{\n                        if (isParticipating) {\n                            setBoxState((prev)=>({\n                                    ...prev,\n                                    isOpening: true\n                                }));\n                            setShowResult(false);\n                        }\n                    },\n                    onBoxEnd: handleBoxComplete,\n                    resultIndex: boxState.resultIndex,\n                    isShaking: boxState.isShaking,\n                    rewardType: rewardTypes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            verified && showResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Apps_components_TaskCompletedCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                points: scoredPoints,\n                title: `Congratulations! You won ${boxState.resultIndex !== null ? rewards[boxState.resultIndex].amount : \"failed to fetch\"} ${rewardTypes}.`,\n                frequency: task.frequency,\n                status: taskParticipation?.status\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                onClick: handleClick,\n                disabled: boxState.isOpening || verified,\n                loading: boxState.isOpening,\n                block: true,\n                rounded: \"full\",\n                children: boxState.isOpening ? \"Opening...\" : \"Open Mystery Box\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Luckydraw\\\\LuckydrawBoxBody.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LuckydrawBoxBody);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Luckydraw/LuckydrawBoxBody.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/CountDownTimer.tsx":
/*!********************************************************!*\
  !*** ./components/Tasks/components/CountDownTimer.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountdownTimer: () => (/* binding */ CountdownTimer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction CountdownTimer({ target }) {\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId = setInterval(()=>{\n            const now = moment__WEBPACK_IMPORTED_MODULE_2___default()();\n            const duration = moment__WEBPACK_IMPORTED_MODULE_2___default().duration(target.diff(now));\n            const days = Math.floor(duration.asDays());\n            const hours = Math.floor(duration.hours());\n            const minutes = Math.floor(duration.minutes());\n            const seconds = Math.floor(duration.seconds());\n            setTimeLeft(`${days ? days.toString() + \" days \" : \"\"}${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`);\n        }, 1000);\n        return ()=>clearInterval(intervalId);\n    }, [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: timeLeft\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\CountDownTimer.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/CountDownTimer.tsx\n");

/***/ }),

/***/ "./components/Tasks/components/TaskCompletedCard.tsx":
/*!***********************************************************!*\
  !*** ./components/Tasks/components/TaskCompletedCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/helpers/frequency */ \"./helpers/frequency.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _CountDownTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CountDownTimer */ \"./components/Tasks/components/CountDownTimer.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Components/FeatureGuard */ \"./components/FeatureGuard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst TaskCompletedCard = ({ points, title, subTitle, frequency, status })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"tasks.completedCard\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const getBackground = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return \"linear-gradient(90deg, #c2410c 0%, #c2640c 50%, #c2800c 100%)\";\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return \"linear-gradient(90deg, #f16363 0%, #f65c5c 50%, #ef4646 100%)\";\n            default:\n                return \"\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Warning, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                    size: 32\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    function getTitle(title, points, globalT) {\n        if (title) {\n            return title;\n        }\n        if (points) {\n            if ((0,_Components_FeatureGuard__WEBPACK_IMPORTED_MODULE_6__.isFeatureEnabled)(\"POINTS\")) {\n                return `${t(\"title.points\", {\n                    points: points,\n                    projectPoints: globalT(\"projectPoints\")\n                })}`;\n            }\n            return `${t(\"title.noPoints\")}`;\n        }\n        return `${t(\"title.noPoints\")}`;\n    }\n    const getSubTitle = ()=>{\n        switch(status){\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_REVIEW:\n                return `${t(\"subtitle.inReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.IN_AI_VERIFICATION:\n                return `${t(\"subtitle.inAIReview\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID:\n                return `${t(\"subtitle.valid\")}`;\n            case _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.INVALID:\n                return `${t(\"subtitle.invalid\")}`;\n            default:\n                return `${t(\"subtitle.valid\")}`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col p-4 py-6 bg-primary rounded-xl relative overflow-hidden gradient-primary text-primary-foreground shadow\",\n        style: {\n            background: getBackground()\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg  mb-0\",\n                                children: getTitle(title, points, globalT)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: frequency && frequency !== _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.Frequency.NONE ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        \"Resets in \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CountDownTimer__WEBPACK_IMPORTED_MODULE_4__.CountdownTimer, {\n                                                target: _Root_helpers_frequency__WEBPACK_IMPORTED_MODULE_1__.frequencyConfig[frequency].cutOff()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : subTitle ? subTitle : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: getSubTitle()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\components\\\\TaskCompletedCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TaskCompletedCard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/components/TaskCompletedCard.tsx\n");

/***/ }),

/***/ "./components/TextEditor.tsx":
/*!***********************************!*\
  !*** ./components/TextEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! draft-js */ \"draft-js\");\n/* harmony import */ var draft_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(draft_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! draft-js/dist/Draft.css */ \"./node_modules/draft-js/dist/Draft.css\");\n/* harmony import */ var draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(draft_js_dist_Draft_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MediaDisplay({ block, contentState }) {\n    const entityKey = block.getEntityAt(0);\n    if (!entityKey) return null;\n    const entity = contentState.getEntity(entityKey);\n    const { src, mediaType } = entity.getData();\n    if (!src) return null;\n    if (mediaType === \"video\" || entity.getType() === \"VIDEO\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n            controls: true,\n            preload: \"metadata\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\",\n                borderRadius: \"6px\",\n                margin: \"8px 0\"\n            },\n            src: src,\n            children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: \"Rich Text Image\",\n        style: {\n            maxWidth: \"100%\",\n            height: \"auto\",\n            borderRadius: \"6px\",\n            margin: \"8px 0\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction mediaBlockRenderer(block) {\n    if (block.getType() === \"atomic\") {\n        return {\n            component: MediaDisplay,\n            editable: false\n        };\n    }\n    return null;\n}\nfunction TextEditor({ readonly = true, defaultValue, expandable = false, maxHeight = 100, className }) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    const [isOverflow, setIsOverflow] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { theme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        const container = ref.current;\n        if (!container || !setIsOverflow) return;\n        if (expandable && container.offsetHeight >= maxHeight) {\n            setIsOverflow(true);\n        }\n    }, [\n        ref,\n        setIsOverflow\n    ]);\n    function Link(props) {\n        const { url } = props.contentState.getEntity(props.entityKey).getData();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: url,\n            target: \"_blank\",\n            rel: \"noreferrer nofollow\",\n            referrerPolicy: \"no-referrer\",\n            style: {\n                color: currentTheme === \"dark\" ? \"#93cefe\" : \"#3b5998\",\n                textDecoration: \"underline\"\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    function findLinkEntities(contentBlock, callback, contentState) {\n        contentBlock.findEntityRanges((character)=>{\n            const entityKey = character.getEntity();\n            return entityKey !== null && contentState.getEntity(entityKey).getType() === \"LINK\";\n        }, callback);\n    }\n    const decorator = new draft_js__WEBPACK_IMPORTED_MODULE_4__.CompositeDecorator([\n        {\n            strategy: findLinkEntities,\n            component: Link\n        }\n    ]);\n    let editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createEmpty(decorator);\n    if (!defaultValue) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    if (defaultValue) {\n        try {\n            const parsedJson = JSON.parse(defaultValue);\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent((0,draft_js__WEBPACK_IMPORTED_MODULE_4__.convertFromRaw)(parsedJson), decorator);\n        } catch (err) {\n            editorState = draft_js__WEBPACK_IMPORTED_MODULE_4__.EditorState.createWithContent(draft_js__WEBPACK_IMPORTED_MODULE_4__.ContentState.createFromText(defaultValue), decorator);\n        }\n    }\n    if (!editorState.getCurrentContent().hasText()) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            isOverflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute z-20 flex items-center w-full justify-center -bottom-3`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretUp, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.CaretDown, {\n                                size: 16,\n                                weight: \"bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: ref,\n                style: expandable && !isExpanded ? {\n                    maxHeight,\n                    marginBottom: 0\n                } : {},\n                className: \"jsx-7a4dfc642d828fbd\" + \" \" + ((0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(`text-base text-ch leading-normal overflow-hidden`, className, isExpanded ? \"mb-10\" : \"\") || \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"7a4dfc642d828fbd\",\n                        children: \".RichEditor-text-align-left,.RichEditor-text-align-left .public-DraftStyleDefault-block{text-align:left!important}.RichEditor-text-align-center,.RichEditor-text-align-center .public-DraftStyleDefault-block{text-align:center!important}.RichEditor-text-align-right,.RichEditor-text-align-right .public-DraftStyleDefault-block{text-align:right!important}\"\n                    }, void 0, false, void 0, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(draft_js__WEBPACK_IMPORTED_MODULE_4__.Editor, {\n                        editorState: editorState,\n                        readOnly: readonly,\n                        onChange: ()=>{},\n                        blockRendererFn: mediaBlockRenderer,\n                        blockStyleFn: (block)=>{\n                            const blockData = block.getData();\n                            const textAlign = blockData.get(\"textAlign\") || \"left\";\n                            let className = \"\";\n                            switch(block.getType()){\n                                case \"blockquote\":\n                                    className = \"RichEditor-blockquote\";\n                                    break;\n                                default:\n                                    className = \"\";\n                            }\n                            className += ` RichEditor-text-align-${textAlign}`;\n                            return className.trim();\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextEditor.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextEditor.tsx\n");

/***/ })

};
;