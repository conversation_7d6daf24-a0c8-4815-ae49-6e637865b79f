"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Tasks_TaskSummary_tsx"],{

/***/ "./components/Tasks/TaskSummary.tsx":
/*!******************************************!*\
  !*** ./components/Tasks/TaskSummary.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useTaskParticipation */ \"./hooks/useTaskParticipation.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./app-store */ \"./components/Tasks/app-store.ts\");\n/* harmony import */ var _app_store_helper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./app-store.helper */ \"./components/Tasks/app-store.helper.ts\");\n/* harmony import */ var _components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./hooks/useGetTasks */ \"./components/Tasks/hooks/useGetTasks.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TaskSummaryTag = (param)=>{\n    let { task, verified, onClick } = param;\n    var _appTask_config, _appTask_config1;\n    _s();\n    const { appType, taskType, title } = task;\n    const appKey = task.appKey;\n    const taskKey = task.taskKey;\n    const app = (0,_app_store__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_app_store_helper__WEBPACK_IMPORTED_MODULE_5__.selectAppByType)(appType, appKey));\n    const appTask = (0,_app_store__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_app_store_helper__WEBPACK_IMPORTED_MODULE_5__.selectTaskByType)(appType, taskType, {\n        appKey,\n        taskKey\n    }));\n    if (!app || !appTask) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 29,\n        columnNumber: 32\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs cursor-pointer component-bg-hover border border-foreground/20 rounded py-1.5 px-2 flex space-x-1 items-center\",\n        onClick: onClick,\n        children: [\n            task.iconUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 relative flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: task.iconUrl,\n                    alt: \"Custom Task Icon\",\n                    className: \"w-full h-full object-cover rounded\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                iconKey: task.taskType,\n                className: \"h-4 w-4 flex-shrink-0\",\n                color: ((_appTask_config = appTask.config) === null || _appTask_config === void 0 ? void 0 : _appTask_config.color) || (app === null || app === void 0 ? void 0 : app.config.color),\n                url: (appTask === null || appTask === void 0 ? void 0 : (_appTask_config1 = appTask.config) === null || _appTask_config1 === void 0 ? void 0 : _appTask_config1.iconUrl) || (app === null || app === void 0 ? void 0 : app.config.iconUrl)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-clamp-1\",\n                children: [\n                    \" \",\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_9__.Check, {\n                size: 16,\n                className: \"text-primary-foreground bg-primary rounded-full p-1 flex-shrink-0\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TaskSummaryTag, \"i5oQXWCLmirFrsWY2Yu1uiZ9Gj4=\", false, function() {\n    return [\n        _app_store__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _app_store__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = TaskSummaryTag;\nconst TaskSummaryList = (param)=>{\n    let { projectEvent, taskIds } = param;\n    _s1();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { data, loading: taskLoading } = (0,_hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_7__.useGetTasks)(projectEvent.id);\n    const { data: taskParticipation, loading: isParticipationLoading } = (0,_Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__.useUserCurrentTaskParticipationMap)(projectEvent.id);\n    let tasks = ((data === null || data === void 0 ? void 0 : data.pTasks) || []).filter((task)=>taskIds.findIndex((id)=>task.id === id) >= 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-1\",\n        children: tasks.map((task, key)=>{\n            var _taskParticipation_get;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskSummaryTag, {\n                task: task,\n                verified: !!((taskParticipation === null || taskParticipation === void 0 ? void 0 : (_taskParticipation_get = taskParticipation.get(task.id || \"\")) === null || _taskParticipation_get === void 0 ? void 0 : _taskParticipation_get.status) == _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.ParticipationStatus.VALID),\n                onClick: ()=>{\n                    if (!task) return;\n                    router.query[\"taskid\"] = task.id;\n                    router.push(router, undefined, {\n                        shallow: true\n                    });\n                }\n            }, key, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\TaskSummary.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(TaskSummaryList, \"v6do6waLHGJSJTOnvcFUAcBmA/Y=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _hooks_useGetTasks__WEBPACK_IMPORTED_MODULE_7__.useGetTasks,\n        _Hooks_useTaskParticipation__WEBPACK_IMPORTED_MODULE_1__.useUserCurrentTaskParticipationMap\n    ];\n});\n_c1 = TaskSummaryList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TaskSummaryList);\nvar _c, _c1;\n$RefreshReg$(_c, \"TaskSummaryTag\");\n$RefreshReg$(_c1, \"TaskSummaryList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/TaskSummary.tsx\n"));

/***/ })

}]);